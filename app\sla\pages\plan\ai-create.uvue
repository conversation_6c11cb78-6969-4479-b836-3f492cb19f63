<template>
  <view class="ai-plan-container">
    <!-- 顶部导航栏 -->
    <sla-navbar title="AI学习计划" @back="goBack"></sla-navbar>

    <scroll-view class="form-container" scroll-y>
      <view class="ai-intro">
        <text class="ai-intro__text">AI将根据您的学习需求和偏好，自动生成个性化的学习计划</text>
        <text class="ai-intro__note">使用AI生成计划将消耗3-5智慧点数</text>
      </view>

      <view class="form-item">
        <text class="form-label">计划类型</text>
        <view class="plan-type-selector">
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': aiPlanForm.planType === 'daily'}"
            @click="selectPlanType('daily')"
          >
            <text class="plan-type-item__text">日计划</text>
          </view>
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': aiPlanForm.planType === 'weekly'}"
            @click="selectPlanType('weekly')"
          >
            <text class="plan-type-item__text">周计划</text>
          </view>
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': aiPlanForm.planType === 'monthly'}"
            @click="selectPlanType('monthly')"
          >
            <text class="plan-type-item__text">月计划</text>
          </view>
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': aiPlanForm.planType === 'custom'}"
            @click="selectPlanType('custom')"
          >
            <text class="plan-type-item__text">自定义</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label required">学习目标</text>
        <textarea class="form-textarea" v-model="aiPlanForm.description" placeholder="请描述你的学习目标，例如：准备高考数学，学习前端开发等"></textarea>
      </view>

      <view class="form-item">
        <text class="form-label required">开始日期</text>
        <picker mode="date" :value="aiPlanForm.startDate" @change="onStartDateChange">
          <view class="picker-item">{{aiPlanForm.startDate || '请选择开始日期'}}</view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">结束日期</text>
        <view class="end-date-display">
          <text class="end-date-text">{{getEndDateDisplay()}}</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">学习风格</text>
        <view class="learning-style-selector">
          <view
            class="learning-style-item"
            :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'visual'}"
            @click="selectLearningStyle('visual')"
          >
            <text class="learning-style-item__icon">👁️</text>
            <text class="learning-style-item__text">视觉型</text>
          </view>
          <view
            class="learning-style-item"
            :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'auditory'}"
            @click="selectLearningStyle('auditory')"
          >
            <text class="learning-style-item__icon">👂</text>
            <text class="learning-style-item__text">听觉型</text>
          </view>
          <view
            class="learning-style-item"
            :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'reading'}"
            @click="selectLearningStyle('reading')"
          >
            <text class="learning-style-item__icon">📚</text>
            <text class="learning-style-item__text">阅读型</text>
          </view>
          <view
            class="learning-style-item"
            :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'kinesthetic'}"
            @click="selectLearningStyle('kinesthetic')"
          >
            <text class="learning-style-item__icon">✍️</text>
            <text class="learning-style-item__text">动手型</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">技能水平</text>
        <view class="skill-level-container">
          <text class="skill-level-text">初学者</text>
          <view class="skill-level-slider-container">
            <slider min="1" max="5" :value="aiPlanForm.skillLevel" show-value @change="onSkillLevelChange" activeColor="#80B8F5" backgroundColor="#E0E0E0" block-color="#FFFFFF"></slider>
          </view>
          <text class="skill-level-text">专家</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">每日学习时间</text>
        <picker :range="studyTimeOptions" :value="studyTimeIndex" @change="onStudyTimeChange">
          <view class="picker-item">{{studyTimeOptions[studyTimeIndex]}}</view>
        </picker>
      </view>

      <view class="form-item" v-if="aiPlanForm.planType === 'custom'">
        <text class="form-label">计划天数</text>
        <view class="custom-days-selector">
          <slider min="1" max="120" :value="aiPlanForm.customDays" show-value @change="onCustomDaysChange" block-size="28" activeColor="#80B8F5" backgroundColor="#E0E0E0" block-color="#FFFFFF" style="width: 100%; height: 50px;"></slider>
          <text class="custom-days-value">{{aiPlanForm.customDays}}天</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">每日任务数</text>
        <view class="subtask-count-selector">
          <slider min="1" max="10" :value="aiPlanForm.subtaskCount" show-value @change="onSubtaskCountChange" block-size="28" activeColor="#80B8F5" backgroundColor="#E0E0E0" block-color="#FFFFFF" style="width: 100%; height: 50px;"></slider>
          <text class="subtask-count-value">{{aiPlanForm.subtaskCount}}个</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">其他要求</text>
        <textarea class="form-textarea" v-model="aiPlanForm.otherRequirements" placeholder="请输入其他特殊要求"></textarea>
      </view>
    </scroll-view>

    <view class="footer">
      <view class="btn-cancel" hover-class="btn-hover" @click="goBack">取消</view>
      <view class="btn-generate" hover-class="btn-hover" @click="generateAiPlan" :class="{'btn-disabled': isGeneratingPlan}">
        <text v-if="isGeneratingPlan">生成中...</text>
        <text v-else>生成计划</text>
      </view>
    </view>
  </view>
</template>

<script>
import planApi from '../../utils/api/plan.js';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';

export default {
  name: 'AiCreatePlan',
  components: {
    SlaNavbar
  },
  data() {
    return {
      aiPlanForm: {
        description: '',
        startDate: '',
        endDate: '',
        learningStyle: 'visual',
        skillLevel: 3,
        studyTimePerDay: '1-2小时',
        otherRequirements: '',
        planType: 'daily',
        customDays: 7,
        subtaskCount: 3
      },
      studyTimeOptions: ['少于1小时', '1-2小时', '2-3小时', '3-4小时', '4小时以上'],
      studyTimeIndex: 1,
      isGeneratingPlan: false
    }
  },
  onLoad() {
    // 初始化表单
    this.resetAiPlanForm();
  },
  methods: {
    goBack() {
      // 返回上一页，如果没有上一页则跳转到计划首页
      try {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          // 如果有上一页，使用navigateBack
          uni.navigateBack();
        } else {
          // 如果没有上一页，直接跳转到首页
          uni.switchTab({
            url: '/pages/plan/index'
          });
        }
      } catch (error) {
        console.error('Error in goBack:', error);
        // 发生错误时跳转到首页
        uni.switchTab({
          url: '/pages/plan/index'
        });
      }
    },

    resetAiPlanForm() {
      // 重置表单
      const today = new Date();

      // 格式化日期
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      this.aiPlanForm = {
        description: '',
        startDate: formatDate(today),
        endDate: formatDate(today), // 默认结束日期为今天
        learningStyle: 'visual',
        skillLevel: 3,
        studyTimePerDay: '1-2小时',
        otherRequirements: '',
        planType: 'daily',
        customDays: 1, // 默认自定义天数为1
        subtaskCount: 3
      };
      this.studyTimeIndex = 1;
    },

    onStartDateChange(e) {
      this.aiPlanForm.startDate = e.detail.value;

      // 根据计划类型自动计算结束日期
      const startDate = new Date(this.aiPlanForm.startDate);
      let endDate = new Date(startDate);

      if (this.aiPlanForm.planType === 'daily') {
        // 日计划结束日期就是开始日期
        this.aiPlanForm.endDate = this.aiPlanForm.startDate;
      } else if (this.aiPlanForm.planType === 'weekly') {
        // 周计划结束日期是开始日期加6天（一周）
        endDate.setDate(startDate.getDate() + 6);
        this.aiPlanForm.endDate = this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'monthly') {
        // 月计划结束日期是下个月的最后一天
        endDate.setMonth(startDate.getMonth() + 1);
        endDate.setDate(0); // 设置为上个月的最后一天
        this.aiPlanForm.endDate = this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'custom') {
        // 自定义计划根据自定义天数计算结束日期
        endDate.setDate(startDate.getDate() + this.aiPlanForm.customDays - 1);
        this.aiPlanForm.endDate = this.formatDate(endDate);
      }
    },

    selectLearningStyle(style) {
      this.aiPlanForm.learningStyle = style;
    },

    onSkillLevelChange(e) {
      this.aiPlanForm.skillLevel = e.detail.value;
    },

    onStudyTimeChange(e) {
      this.studyTimeIndex = e.detail.value;
      this.aiPlanForm.studyTimePerDay = this.studyTimeOptions[this.studyTimeIndex];
    },

    // 选择计划类型
    selectPlanType(type) {
      this.aiPlanForm.planType = type;

      // 根据计划类型设置默认天数
      if (type === 'daily') {
        // 日计划默认1天
        this.aiPlanForm.customDays = 1;
      } else if (type === 'weekly') {
        // 周计划默认7天
        this.aiPlanForm.customDays = 7;
        this.adjustToWeekStart();
      } else if (type === 'monthly') {
        // 月计划默认30天
        this.aiPlanForm.customDays = 30;
        this.adjustToMonthStart();
      } else if (type === 'custom') {
        // 自定义计划默认7天
        this.aiPlanForm.customDays = 7;
      }

      // 重新计算结束日期
      if (this.aiPlanForm.startDate) {
        const startDate = new Date(this.aiPlanForm.startDate);
        let endDate = new Date(startDate);

        if (type === 'daily') {
          // 日计划结束日期就是开始日期
          this.aiPlanForm.endDate = this.aiPlanForm.startDate;
        } else if (type === 'weekly') {
          // 周计划结束日期是开始日期加6天（一周）
          endDate.setDate(startDate.getDate() + 6);
          this.aiPlanForm.endDate = this.formatDate(endDate);
        } else if (type === 'monthly') {
          // 月计划结束日期是下个月的最后一天
          endDate.setMonth(startDate.getMonth() + 1);
          endDate.setDate(0); // 设置为上个月的最后一天
          this.aiPlanForm.endDate = this.formatDate(endDate);
        } else if (type === 'custom') {
          // 自定义计划根据自定义天数计算结束日期
          endDate.setDate(startDate.getDate() + this.aiPlanForm.customDays - 1);
          this.aiPlanForm.endDate = this.formatDate(endDate);
        }
      }
    },

    // 调整到周的开始日期
    adjustToWeekStart() {
      const startDate = new Date(this.aiPlanForm.startDate);
      const day = startDate.getDay(); // 0是周日，1-6是周一到周六
      const diff = day === 0 ? -6 : 1 - day; // 计算到周一的差值
      startDate.setDate(startDate.getDate() + diff);
      this.aiPlanForm.startDate = this.formatDate(startDate);
    },

    // 调整到月的开始日期
    adjustToMonthStart() {
      const startDate = new Date(this.aiPlanForm.startDate);
      startDate.setDate(1);
      this.aiPlanForm.startDate = this.formatDate(startDate);
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 自定义天数变化处理
    onCustomDaysChange(e) {
      this.aiPlanForm.customDays = e.detail.value;

      // 如果是自定义计划类型，重新计算结束日期
      if (this.aiPlanForm.planType === 'custom' && this.aiPlanForm.startDate) {
        const startDate = new Date(this.aiPlanForm.startDate);
        let endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + this.aiPlanForm.customDays - 1);
        this.aiPlanForm.endDate = this.formatDate(endDate);
      }
    },

    // 子任务数量变化处理
    onSubtaskCountChange(e) {
      this.aiPlanForm.subtaskCount = e.detail.value;
    },

    // 获取结束日期显示
    getEndDateDisplay() {
      if (!this.aiPlanForm.startDate) {
        return '请先选择开始日期';
      }

      const startDate = new Date(this.aiPlanForm.startDate);
      let endDate = new Date(startDate);

      // 根据计划类型计算结束日期
      if (this.aiPlanForm.planType === 'daily') {
        // 日计划结束日期就是开始日期
        return this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'weekly') {
        // 周计划结束日期是开始日期加6天（一周）
        endDate.setDate(startDate.getDate() + 6);
        return this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'monthly') {
        // 月计划结束日期是下个月的最后一天
        endDate.setMonth(startDate.getMonth() + 1);
        endDate.setDate(0); // 设置为上个月的最后一天
        return this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'custom') {
        // 自定义计划根据自定义天数计算结束日期
        endDate.setDate(startDate.getDate() + this.aiPlanForm.customDays - 1);
        return this.formatDate(endDate);
      }

      return '未知日期';
    },

    generateAiPlan() {
      // ????
      if (!this.aiPlanForm.description) {
        uni.showToast({
          title: '请输入学习目标',
          icon: 'none'
        });
        return;
      }

      if (!this.aiPlanForm.startDate) {
        uni.showToast({
          title: '请选择开始日期',
          icon: 'none'
        });
        return;
      }

      // 设置生成状态
      this.isGeneratingPlan = true;

      // 确保有结束日期
      if (!this.aiPlanForm.endDate) {
        this.aiPlanForm.endDate = this.aiPlanForm.startDate;
      }

      // 准备提交到API的数据
      const aiPlanData = {
        description: this.aiPlanForm.description,
        start_date: this.aiPlanForm.startDate,
        end_date: this.aiPlanForm.endDate,
        learning_style: this.aiPlanForm.learningStyle || 'visual',
        skill_level: this.aiPlanForm.skillLevel || 3,
        study_time_per_day: this.aiPlanForm.studyTimePerDay || '1-2小时',
        other_requirements: this.aiPlanForm.otherRequirements || '',
        plan_type: this.aiPlanForm.planType || 'daily',
        custom_days: this.aiPlanForm.customDays || 1,
        subtask_count: this.aiPlanForm.subtaskCount || 3
      };

      // 显示加载中
      uni.showToast({
        title: 'AI正在生成中...',
        icon: 'loading',
        duration: 2000
      });

      // 显示智慧点数消耗提示
      setTimeout(() => {
        uni.showToast({
          title: '将消耗智慧点数3-5点',
          icon: 'none',
          duration: 3000
        });
      }, 2500);

      // 返回到上一页或首页
      try {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          // 如果有上一页，使用navigateBack
          uni.navigateBack();
        } else {
          // 如果没有上一页，直接跳转到首页
          uni.switchTab({
            url: '/pages/plan/index'
          });
        }
      } catch (error) {
        console.error('Error in navigating back:', error);
        // 发生错误时跳转到首页
        uni.switchTab({
          url: '/pages/plan/index'
        });
      }

      // 调用API生成AI计划
      console.log('准备调用AI计划生成接口，参数：', JSON.stringify(aiPlanData));
      planApi.generateAiPlan(aiPlanData).then(res => {
        console.log('AI计划生成结果:', JSON.stringify(res));
        if (res.code === 200 && res.data) {
          // 获取计划ID
          const planId = res.data.planId;
          console.log('AI计划生成成功，ID:', planId);

          // 显示成功提示
          uni.showToast({
            title: 'AI计划已生成',
            icon: 'success',
            duration: 2000
          });

          // 获取当前页面栈，准备刷新列表
          const currentPages = getCurrentPages();
          const currentPage = currentPages[currentPages.length - 1];
          console.log('当前页面:', currentPage ? currentPage.route : '未知页面');

          if (currentPage && currentPage.route === 'pages/plan/index') {
            console.log('在计划页面，需要刷新列表');
                          // 尝试刷新计划列表
              if (currentPage.$vm && typeof currentPage.$vm.loadPlans === 'function') {
              setTimeout(() => {
                console.log('刷新计划列表');
                                  // 刷新计划列表
                  if (currentPage && currentPage.$vm) {
                  currentPage.$vm.loadPlans();
                                      // 切换到今日选项卡
                    if (currentPage.$vm && typeof currentPage.$vm.changeTab === 'function') {
                    console.log('切换到今日选项卡');
                    currentPage.$vm.changeTab('today');
                  }
                } else {
                  console.warn('无法刷新计划列表，currentPage或currentPage.$vm不存在');
                }
              }, 500);
            }
          }

                      // 跳转到详情
            setTimeout(() => {
            console.log('准备跳转到计划详情，ID:', planId);
            uni.navigateTo({
              url: `/pages/plan/detail?id=${planId}`,
              success: () => {
                console.log('跳转到计划详情成功');
              },
              fail: (err) => {
                console.error('跳转到计划详情失败:', err);
              }
            });
          }, 1000);
        } else {
          console.error('生成AI计划失败:', res.message);
          uni.showToast({
            title: res.message || '生成失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('生成AI计划失败:', err);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }).finally(() => {
        this.isGeneratingPlan = false;
        console.log('AI计划生成流程结束');
      });
    }
  }
}
</script>

<style>
.ai-plan-container {
  display: flex;
  flex-direction: column;
  min-height: 800px;
  background-color: #F8F9FA;
}

.header {
  height: 56px;
  background-color: #FFF5F5;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  font-size: 20px;
  color: #333333;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.placeholder {
  width: 40px;
}

.form-container {
  flex: 1;
  padding: 16px;
}

.ai-intro {
  background-color: #E6F7FF;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ai-intro__text {
  font-size: 14px;
  color: #1890FF;
  line-height: 1.5;
  display: flex;
  margin-bottom: 8px;
}

.ai-intro__note {
  font-size: 12px;
  color: #ff6b6b;
  line-height: 1.5;
  display: flex;
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 16px;
  background-color: #FFFFFF;
  border-top: 1px solid #EEEEEE;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 16px;
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-label {
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}

.form-textarea {
  width: 100%;
  height: 80px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 8px;
  font-size: 14px;
  color: #333333;
}

.time-range-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.picker-item {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  font-size: 14px;
  color: #333333;
}

.picker-separator {
  padding: 0 8px;
  color: #999999;
}

/* 学习风格选择器 */
.learning-style-selector {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.learning-style-item {
  width: calc(50% - 16px);
  margin: 8px;
  padding: 12px;
  border-radius: 8px;
  background-color: #F5F5F5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.learning-style-item--active {
  background-color: #E6F7FF;
  border: 1px solid #1890FF;
}

.learning-style-item__icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.learning-style-item__text {
  font-size: 14px;
  color: #666666;
}

.learning-style-item--active .learning-style-item__text {
  color: #1890FF;
  font-weight: bold;
}

/* 技能水平容器 */
.skill-level-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
  margin-top: 5px;
}

.skill-level-text {
  font-size: 14px;
  color: #666666;
  min-width: 50px;
}

.skill-level-slider-container {
  flex: 1;
  padding: 0 10px;
}

/* 计划类型选择器 */
.plan-type-selector {
  display: flex;
  flex-direction: row;
  background-color: #F5F5F5;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 8px;
}

.plan-type-item {
  flex: 1;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.plan-type-item--active {
  background-color: #80B8F5;
}

.plan-type-item__text {
  font-size: 14px;
  color: #666666;
}

.plan-type-item--active .plan-type-item__text {
  color: #FFFFFF;
  font-weight: bold;
}

/* 自定义天数选择器 */
.custom-days-selector {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
  background-color: #F8F9FA;
  border-radius: 10px;
  margin-top: 5px;
}

.custom-days-value {
  min-width: 70px;
  text-align: center;
  font-size: 16px;
  color: #333333;
  font-weight: bold;
  background-color: #E6F7FF;
  padding: 5px 10px;
  border-radius: 15px;
  margin-left: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 结束日期显示 */
.end-date-display {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px 15px;
  background-color: #F8F9FA;
  border-radius: 8px;
  border: 1px dashed #E0E0E0;
}

.end-date-text {
  font-size: 16px;
  color: #333333;
  font-weight: bold;
}

/* 子任务数量选择器 */
.subtask-count-selector {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
  background-color: #F8F9FA;
  border-radius: 10px;
  margin-top: 5px;
}

.subtask-count-value {
  min-width: 70px;
  text-align: center;
  font-size: 16px;
  color: #333333;
  font-weight: bold;
  background-color: #E6F7FF;
  padding: 5px 10px;
  border-radius: 15px;
  margin-left: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn-cancel {
  padding: 8px 16px;
  background-color: #F5F5F5;
  border-radius: 20px;
  font-size: 14px;
  color: #666666;
  margin-right: 12px;
  text-align: center;
}

.btn-generate {
  padding: 8px 16px;
  background-color: #80B8F5;
  border-radius: 20px;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
}

.btn-disabled {
  background-color: #CCCCCC !important;
  color: #FFFFFF !important;
  opacity: 0.7;
}

.btn-hover {
  opacity: 0.8;
  transform: scale(0.98);
}
</style>
</template>

