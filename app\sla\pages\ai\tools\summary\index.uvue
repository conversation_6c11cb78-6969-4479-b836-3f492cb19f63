<template>
  <view class="summary-container">
    <!-- 顶部导航栏 -->
    <view class="summary-header">
      <view class="summary-back" @click="goBack">
        <text class="summary-back__icon">←</text>
      </view>
      <text class="summary-title">文档总结</text>
      <view class="summary-actions">
        <view class="summary-action" @click="goToHistory">
          <text class="summary-action__icon">⏱</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="summary-content" scroll-y>
      <!-- 输入方式选择 -->
      <view class="input-method">
        <view
          class="input-method__option"
          :class="{'input-method__option--active': inputMethod === 'text'}"
          @click="switchInputMethod('text')"
        >
          <text class="input-method__option-text">文本输入</text>
        </view>
        <view
          class="input-method__option"
          :class="{'input-method__option--active': inputMethod === 'file'}"
          @click="switchInputMethod('file')"
        >
          <text class="input-method__option-text">文档上传</text>
        </view>
      </view>

      <!-- 文本输入区域 -->
      <view class="text-input-area" v-if="inputMethod === 'text'">
        <view class="text-input-wrapper">
          <textarea
            class="text-input"
            v-model="inputText"
            placeholder="请输入需要总结的文本内容..."
            auto-height
            maxlength="10000"
          ></textarea>
          <view class="text-input-counter">
            <text class="text-input-counter__text">{{ inputText.length }}/10000</text>
          </view>
        </view>

        <!-- 清空按钮 -->
        <view class="text-actions" v-if="inputText.trim().length > 0">
          <view class="text-action text-action--clear" @click="clearTextInput">
            <text class="text-action__text">清空文本</text>
          </view>
        </view>
      </view>

      <!-- 文件上传区域 -->
      <view class="file-upload-area" v-if="inputMethod === 'file'">
        <!-- 上传区域 -->
        <view class="upload-area" @click="selectFile" v-if="!fileSelected">
          <view class="upload-icon">
            <text class="upload-icon__text">📄</text>
          </view>
          <text class="upload-text">选择文档</text>
          <text class="upload-desc">支持TXT、Word、PDF格式</text>
        </view>

        <!-- 已选文件 -->
        <view class="selected-file" v-if="fileSelected">
          <view class="file-info">
            <view class="file-icon">
              <text class="file-icon__text">{{ getFileIcon() }}</text>
            </view>
            <view class="file-details">
              <text class="file-name">{{ fileName }}</text>
              <text class="file-size">{{ fileSize }}</text>
            </view>
          </view>

          <view class="file-actions">
            <view class="file-action file-action--view" @click="viewFile">
              <text class="file-action__text">查看</text>
            </view>
          </view>
        </view>

        <!-- 清空按钮 - 文件上传 -->
        <view class="clear-document-button" v-if="fileSelected" @click="removeFile">
          <text class="clear-document-button__text">清除文档</text>
        </view>
      </view>

      <!-- 思维导图选项 -->
      <view class="mindmap-option">
        <view class="option-label">
          <text class="option-label__text">生成思维导图</text>
        </view>
        <view class="option-switch" @click="toggleMindmap">
          <view class="option-switch__track" :class="{'option-switch__track--active': generateMindmap}">
            <view class="option-switch__thumb" :class="{'option-switch__thumb--active': generateMindmap}"></view>
          </view>
        </view>
      </view>

      <!-- 生成按钮 -->
      <view
        class="generate-button"
        :class="{'generate-button--disabled': !canGenerate}"
        @click="generateSummary"
      >
        <text class="generate-button__text">开始总结</text>
      </view>

      <!-- 生成中 -->
      <view class="generating" v-if="isGenerating">
        <view class="generating-animation">
          <view class="generating-dot"></view>
          <view class="generating-dot"></view>
          <view class="generating-dot"></view>
        </view>
        <text class="generating-text">AI正在智能分析内容...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { generateSummaryFromFile, generateSummaryFromText } from '../../../../utils/api/aitool.js';
import { showProgress, updateNotificationProgress, showSuccess, showError } from '../../../../utils/notificationManager.js';
import { createTask, startTask, updateTaskProgress, completeTask, failTask, TaskType, setTaskCheckFunction } from '../../../../utils/taskManager.js';

// 包装Promise处理错误
const awaitWrap = function(promise) {
  return promise
    .then(data => [null, data])
    .catch(err => [err, null]);
};

export default {
  data() {
    return {
      // 输入方式：'text' 或 'file'
      inputMethod: 'text',

      // 文本输入内容
      inputText: '',

      // 文件相关
      fileSelected: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      fileType: '',

      // 是否生成思维导图
      generateMindmap: false,

      // 生成中
      isGenerating: false,

      // 上传进度
      uploadProgress: 0
    }
  },
  computed: {
    // 是否可以生成
    canGenerate() {
      if (this.isGenerating) return false;

      if (this.inputMethod === 'text') {
        return this.inputText.trim().length > 0;
      } else {
        return this.fileSelected;
      }
    }
  },
  methods: {
    // 返回主页
    goBack() {
      uni.switchTab({
        url: '/pages/ai/index'
      });
    },

    // 跳转到历史记录
    goToHistory() {
      uni.navigateTo({
        url: '/pages/ai/tools/summary/history'
      });
    },

    // 切换输入方式
    switchInputMethod(method) {
      this.inputMethod = method;
    },

    // 选择文件
    selectFile() {
      uni.chooseFile({
        count: 1,
        type: 'all',
        extension: ['.txt', '.doc', '.docx', '.pdf'],
        success: (res) => {
          const file = res.tempFiles[0];
          this.fileName = file.name;
          this.fileSize = this.formatFileSize(file.size);
          this.filePath = file.path;
          this.fileType = this.getFileType(file.name);
          this.fileSelected = true;
        },
        fail: (err) => {
          console.error('选择文件失败:', err);
          uni.showToast({
            title: '选择文件失败',
            icon: 'none'
          });
        }
      });
    },

    // 移除文件
    removeFile() {
      this.fileSelected = false;
      this.fileName = '';
      this.fileSize = '';
      this.filePath = '';
      this.fileType = '';

      // 显示提示
      uni.showToast({
        title: '已清除文档',
        icon: 'success',
        duration: 1500
      });
    },

    // 查看文件
    viewFile() {
      // 确保文件路径存在和有效
      if (!this.filePath) {
        uni.showToast({
          title: '文件路径无效',
          icon: 'none'
        });
        return;
      }

      // 打开文件
      uni.openDocument({
        filePath: this.filePath,
        success: () => {
          console.log('打开文件成功');
        },
        fail: (err) => {
          console.error('打开文件失败:', err);
          uni.showToast({
            title: '打开文件失败',
            icon: 'none'
          });
        }
      });
    },

    // 清空文本输入
    clearTextInput() {
      this.inputText = '';

      // 显示提示
      uni.showToast({
        title: '已清空文本',
        icon: 'success',
        duration: 1500
      });
    },

    // 切换思维导图生成选项
    toggleMindmap() {
      this.generateMindmap = !this.generateMindmap;
    },

    // 获取文件图标
    getFileIcon() {
      switch (this.fileType) {
        case 'txt':
          return '📝';
        case 'doc':
        case 'docx':
          return '📄';
        case 'pdf':
          return '📑';
        default:
          return '📃';
      }
    },

    // 获取文件类型
    getFileType(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      return ext;
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + 'B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + 'KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + 'MB';
      }
    },

    // 生成总结
    async generateSummary() {
      if (!this.canGenerate) return;

      // 设置生成中
      this.isGenerating = true;

      // 创建任务
      const taskId = createTask({
        type: TaskType.AI_SUMMARY,
        title: '文档总结',
        description: this.inputMethod === 'text' ? 
          `总结${this.inputText.substring(0, 20)}...` : 
          `总结文件: ${this.fileName}`,
        createTime: new Date().toISOString()
      });

      // 显示进度条
      showProgress({
        title: '文档总结',
        message: '正在处理数据...',
        progress: 10,
        duration: 0
      });

      try {
        // 开始任务
        startTask(taskId);

        // 根据输入方式调用不同的API
        let result;
        if (this.inputMethod === 'text') {
          // 更新处理进度
          updateTaskProgress(taskId, 20);
          updateNotificationProgress('准备文本数据...', 20);

          // 调用文本总结
          result = await generateSummaryFromText({
            text: this.inputText,
            generateMindmap: this.generateMindmap,
            onProgress: (progress) => {
              // 更新处理和生成进度
              updateTaskProgress(taskId, 20 + progress * 0.6);
              updateNotificationProgress('AI分析中...', 20 + progress * 0.6);
            }
          });
        } else {
          // 更新处理进度
          updateTaskProgress(taskId, 20);
          updateNotificationProgress('准备文件数据...', 20);

          // 调用文件总结
          result = await generateSummaryFromFile({
            filePath: this.filePath,
            fileName: this.fileName,
            fileType: this.fileType,
            generateMindmap: this.generateMindmap,
            onUploadProgress: (progress) => {
              this.uploadProgress = progress;
              // 更新上传进度 (占总进度20%)
              updateTaskProgress(taskId, 20 + progress * 0.2);
              updateNotificationProgress('上传文件...', 20 + progress * 0.2);
            },
            onProcessProgress: (progress) => {
              // 更新处理进度 (占总进度40%)
              updateTaskProgress(taskId, 40 + progress * 0.4);
              updateNotificationProgress('AI分析中...', 40 + progress * 0.4);
            }
          });
        }

        // 完成任务
        completeTask(taskId);

        // 显示成功提示
        showSuccess({
          title: '总结完成',
          message: '文档总结已生成',
          duration: 3000
        });

        // 保存结果到历史记录
        this.saveSummaryToHistory(result);

        // 跳转到结果页面
        uni.navigateTo({
          url: `/pages/ai/tools/summary/result?id=${result.id}`
        });
      } catch (error) {
        console.error('总结失败:', error);

        // 失败任务
        failTask(taskId);

        // 显示错误提示
        showError({
          title: '总结失败',
          message: error.message || '请检查网络连接并重试',
          duration: 3000
        });
      } finally {
        // 重置生成中
        this.isGenerating = false;
      }
    },

    // 保存总结到历史记录
    saveSummaryToHistory(result) {
      try {
        // 获取历史记录
        let history = uni.getStorageSync('summary_history') || [];
        
        // 确保是数组
        if (!Array.isArray(history)) {
          history = [];
        }

        // 添加新记录
        history.unshift(result);

        // 限制历史记录数量
        if (history.length > 100) {
          history = history.slice(0, 100);
        }

        // 保存记录
        uni.setStorageSync('summary_history', history);
      } catch (error) {
        console.error('保存历史记录失败:', error);
      }
    }
  }
}
</script>

<style>
.summary-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

/* 顶部导航栏样式 */
.summary-header {
  height: 44px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%);
  position: relative;
  z-index: 10;
}

.summary-back {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.summary-back__icon {
  font-size: 24px;
  color: #ffffff;
}

.summary-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
}

.summary-actions {
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.summary-action {
  padding: 5px;
}

.summary-action__icon {
  font-size: 20px;
  color: #ffffff;
}

/* 内容区域 */
.summary-content {
  flex: 1;
  padding: 16px;
}

/* 输入方式选择器 */
.input-method {
  display: flex;
  flex-direction: row;
  height: 44px;
  margin-bottom: 20px;
  border-radius: 22px;
  overflow: hidden;
  background-color: #e9ecef;
}

.input-method__option {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  transition: all 0.3s ease;
}

.input-method__option--active {
  background-color: #4A6FE3;
}

.input-method__option-text {
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  transition: all 0.3s ease;
}

.input-method__option--active .input-method__option-text {
  color: #ffffff;
}

/* 文本输入区域样式 */
.text-input-area {
  margin-bottom: 20px;
}

.text-input-wrapper {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.text-input {
  width: 100%;
  min-height: 150px;
  font-size: 16px;
  color: #212529;
  line-height: 1.5;
  padding: 0;
}

.text-input-counter {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.text-input-counter__text {
  font-size: 12px;
  color: #6c757d;
}

.text-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.text-action {
  padding: 8px 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.text-action--clear {
  background-color: #e0e0e0;
}

.text-action__text {
  font-size: 14px;
  color: #495057;
}

/* 文件上传区域样式 */
.file-upload-area {
  margin-bottom: 20px;
}

.upload-area {
  background-color: #ffffff;
  border: 2px dashed #adb5bd;
  border-radius: 12px;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  margin-bottom: 12px;
}

.upload-icon__text {
  font-size: 48px;
  color: #adb5bd;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

.upload-desc {
  font-size: 12px;
  color: #6c757d;
}

.selected-file {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.file-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 12px;
}

.file-icon__text {
  font-size: 32px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 4px;
  display: block;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #6c757d;
}

.file-actions {
  display: flex;
  flex-direction: row;
}

.file-action {
  padding: 8px 16px;
  border-radius: 8px;
  margin-left: 8px;
}

.file-action--view {
  background-color: #e0e0e0;
}

.file-action__text {
  font-size: 14px;
  color: #495057;
}

.clear-document-button {
  margin-top: 12px;
  padding: 10px 0;
  border-radius: 8px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.clear-document-button__text {
  font-size: 14px;
  color: #495057;
}

/* 思维导图选项样式 */
.mindmap-option {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.option-label__text {
  font-size: 16px;
  font-weight: 500;
  color: #212529;
}

.option-switch {
  position: relative;
}

.option-switch__track {
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: #e0e0e0;
  transition: all 0.3s ease;
}

.option-switch__track--active {
  background-color: #4A6FE3;
}

.option-switch__thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.option-switch__thumb--active {
  transform: translateX(20px);
}

/* 生成按钮样式 */
.generate-button {
  margin-top: 24px;
  height: 50px;
  border-radius: 25px;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 8px rgba(74, 111, 227, 0.3);
  transition: all 0.3s ease;
}

.generate-button--disabled {
  opacity: 0.6;
  background: linear-gradient(135deg, #a0a0a0 0%, #c0c0c0 100%);
  box-shadow: none;
}

.generate-button__text {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
}

/* 生成中动画样式 */
.generating {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.generating-animation {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.generating-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #4A6FE3;
  margin: 0 4px;
  animation: bounce 1.5s infinite ease-in-out;
}

.generating-dot:nth-child(1) {
  animation-delay: 0s;
}

.generating-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.generating-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.generating-text {
  font-size: 16px;
  color: #4A6FE3;
  font-weight: 500;
}
</style>
</template>
