<template>
  <view class="time-container">
    <!-- 使用通用导航栏 -->
    <sla-navbar title="时间管理" :show-back="false">
      <template #right>
        <view class="history-button" @click="navigateToFocusRecords">
          <text class="history-button__text">历史</text>
        </view>
      </template>
    </sla-navbar>

    <!-- Tab切换 -->
    <view class="time-tabs">
      <view class="time-tab" :class="{'time-tab--active': activeTab === 'timer'}" @click="switchTab('timer')">
        <text class="time-tab__text">计时器</text>
      </view>
      <view class="time-tab" :class="{'time-tab--active': activeTab === 'studyroom'}" @click="switchTab('studyroom')">
        <text class="time-tab__text">自习室</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="time-content" scroll-y>
      <!-- 计时器Tab内容 -->
      <time-timer v-if="activeTab === 'timer'"
        @navigate-to-ai="navigateToAI"
        @open-records="openFocusRecords"
      ></time-timer>

      <!-- 自习室Tab内容 -->
      <time-study-room-container v-if="activeTab === 'studyroom'"
        @navigate-to-ai="navigateToAI"
        ref="timeStudyRoom"
      ></time-study-room-container>
    </scroll-view>

    <!-- AI推荐弹窗 -->
    <uni-popup ref="aiRecommendPopup" type="center">
      <view class="ai-recommend-popup">
        <view class="ai-recommend-popup__header">
          <text class="ai-recommend-popup__title">AI推荐</text>
          <view class="ai-recommend-popup__close" @click="closeAIRecommend">
            <text class="ai-recommend-popup__close-text">关闭</text>
          </view>
        </view>
        <view class="ai-recommend-popup__content">
          <text class="ai-recommend-popup__text">{{ aiRecommendation }}</text>
          <view class="ai-recommend-popup__actions">
            <view class="ai-recommend-popup__action-button ai-recommend-popup__action-button--secondary" @click="closeAIRecommend">
              <text class="ai-recommend-popup__action-button-text">取消</text>
            </view>
            <view class="ai-recommend-popup__action-button ai-recommend-popup__action-button--primary" @click="acceptAIRecommend">
              <text class="ai-recommend-popup__action-button-text">接受</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 自定义标签栏 -->
    <custom-tab-bar
      :current="3"
      :showAiToolbox="false"
      @centerClick="navigateToAI"
      @tabClick="handleTabClick"
    ></custom-tab-bar>
  </view>
</template>

<script lang="ts">
import { ref, computed, reactive } from 'vue';
import { IRefs } from '../../utils/types';
import CustomTabBar from '../../components/common/CustomTabBar.uvue';
import TimeTimer from './TimeTimer.uvue';
import TimeStudyRoomContainer from '../../components/time/TimeStudyRoomContainer.uvue';
import UniPopup from '../../uni_modules/uni-popup/components/uni-popup/uni-popup.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';

interface FocusRecord {
  id: number;
  date: string;
  duration: number;
  task: string;
}

interface PopupRefs {
  aiRecommendPopup: {
    open: () => void;
    close: () => void;
  };
  timeStudyRoom?: {
    studyRoomMode: string;
    currentStudyRoom: any;
  };
}

export default {
  components: {
    CustomTabBar,
    TimeTimer,
    TimeStudyRoomContainer,
    UniPopup,
    SlaNavbar
  },
  $refs: {} as PopupRefs,
  data() {
    return {
      // Tab切换
      activeTab: 'timer' as string, // 'timer' ? 'studyroom'

      // AI推荐
      aiRecommendation: 'AI推荐内容',

      // 刷新自习室列表
      _refreshingStudyRoomList: false
    }
  },
  onShow() {
    // 检查是否需要刷新自习室列表
    try {
      const needRefresh = uni.getStorageSync('needRefreshStudyRoomList');
      if (needRefresh) {
        // 清除需要刷新自习室列表的标志
        uni.removeStorageSync('needRefreshStudyRoomList');

        // 如果当前是自习室页面，则延迟刷新自习室列表
        if (this.activeTab === 'studyroom') {
          // 使用setTimeout延迟刷新自习室列表
          setTimeout(() => {
            this.refreshStudyRoomList();
          }, 100);
        }
      }
    } catch (e) {
      console.error('检查需要刷新自习室列表失败', e);
      // 尝试清除需要刷新自习室列表的标志
      try {
        uni.removeStorageSync('needRefreshStudyRoomList');
      } catch (clearError) {
        console.error('清除需要刷新自习室列表标志失败', clearError);
      }
    }
  },

  methods: {
    // Tab切换
    switchTab(tab) {
      // 检查是否在自习室页面且自习室正在使用中
      if (this.activeTab === 'studyroom' && this.isInStudyRoom()) {
        uni.showToast({
          title: '无法切换，请先退出自习室',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 切换到指定页面
      this.activeTab = tab;

      // 如果切换到自习室页面，则清除需要刷新自习室列表的标志
      if (tab === 'studyroom') {
        const needRefresh = uni.getStorageSync('needRefreshStudyRoomList');
        if (needRefresh) {
          // 清除需要刷新自习室列表的标志
          uni.removeStorageSync('needRefreshStudyRoomList');
        }
      }
    },

    // 检查是否在自习室
    isInStudyRoom() {
      // 获取自习室组件
      const studyRoomComponent = this.$refs.timeStudyRoom;
      if (!studyRoomComponent) return false;

      // 检查自习室是否正在使用中
      return (studyRoomComponent as any).studyRoomMode === 'study' &&
             (studyRoomComponent as any).currentStudyRoom !== null;
    },

    // 导航到AI页面
    navigateToAI(showToolbox = false) {
      uni.switchTab({
        url: '/pages/ai/index'
      });

      // 设置AI工具箱标志
      uni.setStorageSync('showAiToolbox', showToolbox);
    },

    // 处理标签点击
    handleTabClick(index) {
      console.log('标签被点击:', index);
    },

    // 打开学习记录
    openFocusRecords() {
      this.navigateToFocusRecords();
    },

    // 打开AI推荐
    openAIRecommend() {
      if (this.$refs.aiRecommendPopup) {
        (this.$refs.aiRecommendPopup as unknown as IRefs['aiRecommendPopup']).open();
      } else {
        console.warn('aiRecommendPopup未定义');
      }
    },

    // 关闭AI推荐
    closeAIRecommend() {
      if (this.$refs.aiRecommendPopup) {
        (this.$refs.aiRecommendPopup as unknown as IRefs['aiRecommendPopup']).close();
      } else {
        console.warn('aiRecommendPopup未定义');
      }
    },

    // 接受AI推荐
    acceptAIRecommend() {
      uni.showToast({
        title: 'AI推荐已接受',
        icon: 'success'
      });
      this.closeAIRecommend();
    },

    // 导航到学习记录页面
    navigateToFocusRecords() {
      uni.navigateTo({
        url: '/pages/time/FocusRecords',
        fail: (err) => {
          console.error('导航到学习记录页面失败', err);
          uni.showToast({
            title: '导航到学习记录页面失败',
            icon: 'none'
          });
        }
      });
    },

    // 刷新自习室列表
    refreshStudyRoomList() {
      console.log('开始刷新自习室列表');
      
      // 检查是否正在刷新自习室列表
      if (this._refreshingStudyRoomList) {
        console.warn('正在刷新自习室列表，忽略本次刷新');
        return;
      }
      
      // 设置正在刷新自习室列表标志
      this._refreshingStudyRoomList = true;
      
      // 尝试刷新自习室列表
      try {
        const studyRoomComponent = this.$refs.timeStudyRoom;
        if (studyRoomComponent) {
          // 调用refreshRoomList方法刷新自习室列表
          if (typeof studyRoomComponent.refreshRoomList === 'function') {
            studyRoomComponent.refreshRoomList();
            console.log('自习室列表刷新成功');
          } else {
            console.warn('自习室列表刷新方法不存在，尝试通过emit刷新');
            // 尝试通过emit刷新自习室列表
            setTimeout(() => {
              uni.$emit('studyRoomListRefresh');
            }, 100);
          }
        } else {
          console.warn('自习室组件不存在，尝试通过emit刷新');
          // 尝试通过emit刷新自习室列表
          setTimeout(() => {
            uni.$emit('studyRoomListRefresh');
          }, 100);
        }
      } catch (error) {
        console.error('刷新自习室列表失败', error);
        // 尝试通过emit刷新自习室列表
        setTimeout(() => {
          uni.$emit('studyRoomListRefresh');
        }, 100);
      } finally {
        // 重置正在刷新自习室列表标志
        setTimeout(() => {
          this._refreshingStudyRoomList = false;
        }, 500);
      }
    }
  }
}
</script>

<style>
.time-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  padding-bottom: 68px; /* 底部填充 */
  position: relative;
  background-image: linear-gradient(to bottom, #eef2ff, #f5f7fa); /* 渐变背景 */
}

/* 历史按钮样式 */
.history-button {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  margin: 8px 0; /* 顶部和底部边距 */
  height: 28px; /* 高度 */
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.history-button:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
}

.history-button__text {
  font-size: 12px;
  color: #FFFFFF;
  font-weight: bold;
}

/* Tab切换 */
.time-tabs {
  display: flex;
  flex-direction: row;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 14px;
  margin: 16px 16px 0;
  box-shadow: 0 3px 10px rgba(31, 60, 136, 0.05);
  overflow: hidden;
  padding: 3px;
}

.time-tab {
  flex: 1;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.time-tab--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.time-tab__text {
  font-size: 14px;
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.time-tab--active .time-tab__text {
  color: #FFFFFF;
  font-weight: bold;
}

.time-content {
  flex: 1;
  padding: 16px;
  padding-bottom: 24px;
}

/* AI推荐弹窗样式 */
.ai-recommend-popup {
  width: 320px;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(31, 60, 136, 0.15);
}

.ai-recommend-popup__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  background-color: #F8F9FA;
}

.ai-recommend-popup__title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
}

.ai-recommend-popup__close {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 999px;
  background-color: rgba(91, 127, 255, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.ai-recommend-popup__close:active {
  transform: scale(0.92);
  background-color: rgba(91, 127, 255, 0.2);
}

.ai-recommend-popup__close-text {
  font-size: 16px;
  color: #5B7FFF;
  font-weight: bold;
}

.ai-recommend-popup__content {
  padding: 16px;
}

.ai-recommend-popup__text {
  font-size: 14px;
  color: #2E3A59;
  line-height: 1.6;
  margin-bottom: 20px;
  background-color: rgba(91, 127, 255, 0.05);
  padding: 12px;
  border-radius: 12px;
  border-left: 3px solid #5B7FFF;
}

.ai-recommend-popup__actions {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 10px;
}

.ai-recommend-popup__action-button {
  padding: 10px 16px;
  border-radius: 12px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.1);
}

.ai-recommend-popup__action-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 6px rgba(31, 60, 136, 0.15);
}

.ai-recommend-popup__action-button--primary {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
}

.ai-recommend-popup__action-button--secondary {
  background-color: #EEF2FF;
}

.ai-recommend-popup__action-button-text {
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0.2px;
}

.ai-recommend-popup__action-button--primary .ai-recommend-popup__action-button-text {
  color: #FFFFFF;
}

.ai-recommend-popup__action-button--secondary .ai-recommend-popup__action-button-text {
  color: #5B7FFF;
}
</style>
</template>

