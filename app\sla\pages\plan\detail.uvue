<template>
  <view class="plan-detail-container">
    <!-- 顶部导航栏 -->
    <sla-navbar title="计划详情" @back="goBack"></sla-navbar>

    <scroll-view class="detail-container" scroll-y v-if="plan">
      <view class="plan-detail-header">
        <text class="plan-detail-title">{{plan.title}}</text>
        <view class="plan-detail-badges">
          <view class="plan-badge plan-badge--ai" v-if="plan.aiGenerated">AI生成</view>
          <view
            class="plan-priority-badge"
            v-if="plan.priority"
            :class="{
              'plan-priority-badge--low': plan.priority === 'low',
              'plan-priority-badge--medium': plan.priority === 'medium',
              'plan-priority-badge--high': plan.priority === 'high'
            }"
          >
            {{ getPriorityText(plan.priority) }}
          </view>
          <view class="plan-type-badge">
            {{ getPlanTypeText(plan.type) }}
          </view>
        </view>
        <view class="plan-detail-time-range">
          <text class="plan-detail-date" v-if="plan.date">开始日期：{{formatDate(plan.date)}}</text>
          <text class="plan-detail-date" v-if="plan.endDate && plan.endDate !== plan.date">结束日期：{{formatDate(plan.endDate)}}</text>
        </view>

        <!-- 打卡功能 -->
        <view class="check-in-section" v-if="!isPlanCompleted() && isCurrentPlan()">
          <view
            class="check-in-button"
            :class="{'check-in-button--completed': plan.todayCompleted}"
            @click="handleCheckIn"
          >
            <text class="check-in-icon">{{ plan.todayCompleted ? '✓' : '📝' }}</text>
            <text class="check-in-text">{{ plan.todayCompleted ? '今日已完成' : '今日打卡' }}</text>
          </view>
          <text class="last-check-in-time" v-if="plan.lastCheckInTime">上次打卡: {{formatDateTime(plan.lastCheckInTime)}}</text>
        </view>
      </view>

      <view class="plan-detail-section" v-if="plan.description">
        <text class="section-title">计划描述</text>
        <text class="plan-detail-description__text">{{plan.description}}</text>
      </view>

      <view class="plan-detail-section" v-if="plan.aiGenerated && plan.learningStyle">
        <text class="section-title">学习风格</text>
        <view class="learning-style-info">
          <text class="learning-style-icon">{{ getLearningStyleIcon(plan.learningStyle) }}</text>
          <text class="learning-style-text">{{ getLearningStyleText(plan.learningStyle) }}</text>
        </view>
      </view>

      <view class="plan-detail-section" v-if="plan.aiGenerated && plan.skillLevel">
        <text class="section-title">技能水平</text>
        <view class="skill-level-info">
          <progress :percent="plan.skillLevel * 20" stroke-width="12" activeColor="#80B8F5" backgroundColor="#F0F0F0" />
          <text class="skill-level-text">{{plan.skillLevel}}/5</text>
        </view>
      </view>

      <view class="plan-detail-section" v-if="plan.subTasks && plan.subTasks.length > 0">
        <view class="section-header">
          <text class="section-title">子任务</text>
          <view class="subtask-actions" v-if="(plan.saved || !plan.aiGenerated) && !isPlanCompleted()">
            <view class="subtask-action-btn" @click="showAddSubtaskModal">
              <text class="subtask-action-icon">+</text>
            </view>
          </view>
        </view>

        <!-- 进度显示区域 -->
        <view class="plan-progress-container">
          <view class="plan-progress-header">
            <text class="plan-progress-title">进度</text>
            <view class="plan-progress-stats">
              <text class="plan-progress-stats-text">共 {{ plan.subTasks.length }} 个任务</text>
            </view>
          </view>
        </view>

        <view class="subtask-list">
          <view class="subtask-item" v-for="(subtask, index) in plan.subTasks" :key="index">
            <view class="subtask-content">
              <text class="subtask-title">{{subtask.title}}</text>
              <text class="subtask-time" v-if="subtask.startTime && subtask.endTime">{{subtask.startTime}} - {{subtask.endTime}}</text>
              <text class="subtask-description" v-if="subtask.description">{{subtask.description}}</text>
            </view>
            <view class="subtask-actions-menu" v-if="(plan.saved || !plan.aiGenerated) && !isPlanCompleted()">
              <view class="subtask-edit-btn" @click="editSubtask(index)">
                <text class="subtask-edit-icon">✏️</text>
              </view>
              <view class="subtask-delete-btn" @click="deleteSubtask(index)">
                <text class="subtask-delete-icon">🗑️</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="plan-detail-section">
        <view class="section-header">
          <text class="section-title">推荐学习资源</text>
          <view class="ai-recommend-btn" @click="generateAiRecommendations" v-if="!isGeneratingResources">
            <text class="ai-recommend-icon">🤖</text>
            <text class="ai-recommend-text">AI推荐</text>
          </view>
          <view class="ai-recommend-btn ai-recommend-btn--loading" v-else>
            <text class="ai-recommend-text">生成中...</text>
          </view>
        </view>

        <view class="resource-list" v-if="plan.recommendedResources && plan.recommendedResources.length > 0">
          <view class="resource-item" v-for="(resource, index) in plan.recommendedResources" :key="index" @click="openResource(resource)">
            <view class="resource-icon" :class="'resource-icon--' + resource.type">
              <text class="resource-icon__text">{{getResourceTypeIcon(resource.type)}}</text>
            </view>
            <view class="resource-content">
              <text class="resource-title">{{resource.title}}</text>
              <text class="resource-desc">{{resource.description}}</text>
              <view class="resource-meta">
                <text class="resource-difficulty">难度: {{resource.difficulty}}/5</text>
                <text class="resource-relevance">相关性: {{resource.relevance}}/5</text>
              </view>
            </view>
            <text class="resource-arrow">></text>
          </view>
        </view>

        <view class="empty-resources" v-else>
          <text class="empty-resources__text">点击"AI推荐"获取个性化学习资源</text>
        </view>
      </view>
    </scroll-view>

    <!-- 子任务编辑模态框 -->
    <view class="modal" v-if="showSubtaskModal">
      <view class="modal-mask" @click="closeSubtaskModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{ isEditingSubtask ? '编辑子任务' : '添加子任务' }}</text>
          <view class="modal-close" @click="closeSubtaskModal">×</view>
        </view>
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label required">任务标题</text>
            <input class="form-input" v-model="subtaskForm.title" placeholder="请输入任务标题" />
          </view>

          <view class="form-item">
            <text class="form-label">时间范围</text>
            <view class="time-range-picker">
              <picker mode="time" :value="subtaskForm.startTime" @change="onSubTaskStartTimeChange">
                <view class="picker-item">{{subtaskForm.startTime || '开始时间'}}</view>
              </picker>
              <text class="picker-separator">至</text>
              <picker mode="time" :value="subtaskForm.endTime" @change="onSubTaskEndTimeChange">
                <view class="picker-item">{{subtaskForm.endTime || '结束时间'}}</view>
              </picker>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">描述</text>
            <textarea class="form-textarea" v-model="subtaskForm.description" placeholder="请输入任务描述（可选）"></textarea>
          </view>
        </view>
        <view class="modal-footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeSubtaskModal">取消</view>
          <view class="btn-primary" hover-class="btn-hover" @click="saveSubtask">保存</view>
        </view>
      </view>
    </view>

    <view class="footer" v-if="plan">
      <view class="footer-buttons">
        <view class="btn-cancel" hover-class="btn-hover" @click="goBack">返回列表</view>
        <view class="btn-primary" hover-class="btn-hover" @click="savePlan" v-if="plan.aiGenerated && !plan.saved && !isPlanCompleted()">保存计划</view>
        <view class="btn-edit" hover-class="btn-hover" @click="editPlan" v-if="(plan.saved || !plan.aiGenerated) && !isPlanCompleted()">编辑</view>
        <view class="btn-delete" hover-class="btn-hover" @click="deletePlan">删除</view>
      </view>
    </view>
  </view>
</template>

<script>
import planApi from '../../utils/api/plan';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';

export default {
  name: 'PlanDetail',
  components: {
    SlaNavbar
  },
  data() {
    return {
      planId: 0,
      plan: null,
      isGeneratingResources: false,
      showSubtaskModal: false,
      isEditingSubtask: false,
      editingSubtaskIndex: -1,
      subtaskForm: {
        title: '',
        startTime: '09:00',
        endTime: '10:00',
        description: ''
      }
    }
  },
  onReady() {
    // 确保 $pageLayoutInstance 存在
    try {
      if (this.$scope && !this.$scope.$pageLayoutInstance) {
        console.log('onReady: 初始化$pageLayoutInstance为空对象');
        this.$scope.$pageLayoutInstance = {};
      }
    } catch (e) {
      console.error('onReady: 设置$pageLayoutInstance失败:', e);
    }
  },

  onLoad(options) {
    try {
      // 确保隐藏任何可能存在的加载状态
      try {
        uni.hideLoading();
      } catch (e) {
        console.error('隐藏加载状态失败', e);
      }

      // 确保 $pageLayoutInstance 存在
      try {
        if (this.$scope && !this.$scope.$pageLayoutInstance) {
          console.log('初始化$pageLayoutInstance为空对象');
          this.$scope.$pageLayoutInstance = {};
        }
      } catch (e) {
        console.error('设置$pageLayoutInstance失败:', e);
      }

      if (options.id) {
        this.planId = parseInt(options.id);
        this.loadPlanData();
      } else {
        uni.showToast({
          title: '计划ID不存在',
          icon: 'none'
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('onLoad出错:', error);
      uni.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },
  methods: {
    loadPlanData() {
      // 显示加载状态
      uni.showLoading({
        title: '加载中...'
      });

      // 调用API获取计划详情
      planApi.getPlanDetail(this.planId).then(res => {
        if (res.code === 200 && res.data) {
          // 转换数据格式为前端格式
          this.plan = {
            id: res.data.planId,
            title: res.data.title,
            type: res.data.type,
            date: res.data.date,
            endDate: res.data.endDate,
            priority: res.data.priority,
            description: res.data.description,
            completed: res.data.completed,
            todayCompleted: res.data.todayCompleted,
            aiGenerated: res.data.aiGenerated,
            createTime: res.data.createTime,
            saved: true, // 从数据库获取的都是已保存的
            subTasks: res.data.subTasks ? res.data.subTasks.map(subTask => {
              return {
                id: subTask.subTaskId,
                title: subTask.title,
                startTime: subTask.startTime,
                endTime: subTask.endTime,
                completed: subTask.completed,
                description: subTask.description
              };
            }) : [],
            recommendedResources: res.data.recommendedResources || []
          };
          console.log('计划详情加载成功:', this.plan);
        } else {
          console.error('获取计划详情失败:', res.message);
          uni.showToast({
            title: '获取计划详情失败',
            icon: 'none'
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      }).catch(err => {
        console.error('获取计划详情失败:', err);

        // 处理特殊的错误情况
        if (err && err.code === 401) {
          // 未登录或登录过期，跳转到登录页
          uni.showToast({
            title: '请先登录后查看',
            icon: 'none',
            duration: 2000
          });

          // 跳转到登录页面
          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        } else {
          // 其他错误
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });

          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      }).finally(() => {
        uni.hideLoading();
      });
    },

    goBack() {
      try {
        // 确保隐藏任何加载状态
        try {
          uni.hideLoading();
        } catch (e) {
          console.error('隐藏加载失败', e);
        }

        // 检查登录状态
        const needLogin = uni.getStorageSync('needLogin');
        if (needLogin) {
          console.log('检测到需要登录标志');

          // 清除标志
          uni.removeStorageSync('needLogin');

          // 跳转到登录页面
          uni.navigateTo({
            url: '/pages/user/login'
          });
          return;
        }

        console.log('正常返回上一页');

        // 检查页面栈深度
        const pages = getCurrentPages();
        if (pages.length > 1) {
          // 如果有上一页，使用navigateBack
          uni.navigateBack();
        } else {
          // 如果没有上一页，跳转到计划首页，使用switchTab
          uni.switchTab({
            url: '/pages/plan/index'
          });
        }
      } catch (error) {
        console.error('goBack方法出错:', error);
        // 发生错误时使用switchTab
        uni.switchTab({
          url: '/pages/plan/index'
        });
      }
    },

    // 计算计划进度百分比
    calculateProgress() {
      if (!this.plan || !this.plan.subTasks || this.plan.subTasks.length === 0) {
        return 0;
      }

      const totalTasks = this.plan.subTasks.length;
      const completedTasks = this.plan.subTasks.filter(task => task.completed).length;

      return Math.round((completedTasks / totalTasks) * 100);
    },

    savePlan() {
      if (!this.plan) return;

      // 显示加载状态
      uni.showLoading({
        title: '保存中...'
      });

      // 构建请求数据
      const planData = {
        title: this.plan.title,
        type: this.plan.type,
        date: this.plan.date,
        endDate: this.plan.endDate || this.plan.date,
        priority: this.plan.priority || 'medium',
        description: this.plan.description || '',
        subTasks: this.plan.subTasks ? this.plan.subTasks.map(subTask => {
          return {
            subTaskId: subTask.subTaskId, // 保留原有的子任务ID
            title: subTask.title,
            startTime: subTask.startTime,
            endTime: subTask.endTime,
            description: subTask.description || ''
          };
        }) : []
      };

      // 调用API保存计划
      planApi.updatePlan(this.planId, planData).then(res => {
        if (res.code === 200) {
          // 更新保存状态
          if (this.plan) {
            this.plan.saved = true;
          }

          // 显示成功提示
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        } else {
          console.error('保存计划失败:', res.message);
          uni.showToast({
            title: res.message || '保存失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('保存计划失败:', err);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }).finally(() => {
        uni.hideLoading();
      });
    },

    editPlan() {
      if (!this.plan) return;

      // ?????????????ID
      uni.navigateTo({
        url: `/pages/plan/edit?id=${this.planId}`,
        fail: (err) => {
          console.error('Failed to navigate to edit page:', err);
          uni.showToast({
            title: '????????',
            icon: 'none'
          });
        }
      });
    },

    deletePlan() {
      uni.showModal({
        title: '????',
        content: '??????????',
        success: (res) => {
          if (res.confirm) {
            // ??????            uni.showLoading({
              title: '????..'
            });

            // ??API????
            planApi.deletePlan(this.planId).then(res => {
              if (res.code === 200) {
                // ????
                uni.showToast({
                  title: '??????,
                  icon: 'success'
                });

                // ??????                setTimeout(() => {
                  uni.navigateBack();
                }, 1500);
              } else {
                console.error('??????:', res.message);
                uni.showToast({
                  title: res.message || '??????',
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('??????:', err);
              uni.showToast({
                title: '??????????',
                icon: 'none'
              });
            }).finally(() => {
              uni.hideLoading();
            });
          }
        }
      });
    },

    openResource(resource) {
      if (resource.url) {
        // ????????
        uni.setClipboardData({
          data: resource.url,
          success: () => {
            uni.showModal({
              title: '????',
              content: `???????????????????\n\n${resource.url}`,
              confirmText: '??',
              cancelText: '??',
              success: (res) => {
                if (res.confirm) {
                  // ???????????
                  // ????????????????????
                  // #ifdef APP-PLUS
                  plus.runtime.openURL(resource.url);
                  // #endif

                  // #ifdef H5
                  window.open(resource.url, '_blank');
                  // #endif

                  // #ifdef MP
                  uni.showToast({
                    title: '????????????',
                    icon: 'none'
                  });
                  // #endif
                }
              }
            });
          }
        });
      } else {
        uni.showToast({
          title: '????????,
          icon: 'none'
        });
      }
    },

    // ??AI??????
    generateAiRecommendations() {
      if (!this.plan) return;

      this.isGeneratingResources = true;

      // ??????
      uni.showLoading({
        title: 'AI??????...',
        mask: true
      });

      // ??AI????API
      planApi.recommendAiResources(this.planId).then(res => {
        if (res.code === 200 && res.data) {
          // ??????
          if (this.plan) {
            this.plan.recommendedResources = res.data || [];
          }

          // ????
          uni.showToast({
            title: '??????',
            icon: 'success'
          });
        } else {
          throw new Error(res.message || '????????');
        }
      }).catch(err => {
        console.error('????????:', err);
        uni.showToast({
          title: err.message || '??????????',
          icon: 'none'
        });
      }).finally(() => {
        this.isGeneratingResources = false;
        uni.hideLoading();
      });
    },

    // ????????
    getResourceTypeIcon(type) {
      const icons = {
        'book': '??',
        'video': '??',
        'article': '??',
        'course': '??',
        'tool': '??',
        'audio': '??'
      };
      return icons[type] || '??';
    },

    // ????????
    getLearningStyleIcon(style) {
      const icons = {
        'visual': '????,
        'auditory': '??',
        'reading': '??',
        'kinesthetic': '??
      };
      return icons[style] || '??';
    },

    // ????????
    getLearningStyleText(style) {
      const texts = {
        'visual': '???????,
        'auditory': '???????,
        'reading': '???????,
        'kinesthetic': '???????
      };
      return texts[style] || '';
    },

    // ??????????
    showAddSubtaskModal() {
      // ??????      this.subtaskForm = {
        title: '',
        startTime: '09:00',
        endTime: '10:00',
        description: ''
      };

      this.isEditingSubtask = false;
      this.editingSubtaskIndex = -1;
      this.showSubtaskModal = true;
    },

    // ??????????
    editSubtask(index) {
      if (!this.plan || !this.plan.subTasks || index < 0 || index >= this.plan.subTasks.length) return;

      const subtask = this.plan.subTasks[index];
      this.subtaskForm = {
        title: subtask.title || '',
        startTime: subtask.startTime || '09:00',
        endTime: subtask.endTime || '10:00',
        description: subtask.description || ''
      };

      this.isEditingSubtask = true;
      this.editingSubtaskIndex = index;
      this.showSubtaskModal = true;
    },

    // ????????
    closeSubtaskModal() {
      this.showSubtaskModal = false;
    },

    // ??????    saveSubtask() {
      // ????
      if (!this.subtaskForm.title) {
        uni.showToast({
          title: '????????',
          icon: 'none'
        });
        return;
      }

      // ????
      if (!this.subtaskForm.startTime || !this.subtaskForm.endTime) {
        uni.showToast({
          title: '?????????',
          icon: 'none'
        });
        return;
      }

      // ??????
      const startMinutes = this.timeToMinutes(this.subtaskForm.startTime);
      const endMinutes = this.timeToMinutes(this.subtaskForm.endTime);
      if (startMinutes >= endMinutes) {
        uni.showToast({
          title: '?????????????,
          icon: 'none'
        });
        return;
      }

      if (!this.plan) {
        uni.showToast({
          title: '????????,
          icon: 'none'
        });
        return;
      }

      if (!this.plan.subTasks) {
        this.plan.subTasks = [];
      }

      if (this.isEditingSubtask && this.editingSubtaskIndex >= 0 && this.editingSubtaskIndex < this.plan.subTasks.length) {
        // ????????        this.plan.subTasks[this.editingSubtaskIndex] = {
          ...this.plan.subTasks[this.editingSubtaskIndex],
          title: this.subtaskForm.title,
          startTime: this.subtaskForm.startTime,
          endTime: this.subtaskForm.endTime,
          description: this.subtaskForm.description
        };
      } else {
        // ??????
        const newSubtask: SubTask = {
          id: this.plan.subTasks.length + 1, // ????ID
          title: this.subtaskForm.title,
          startTime: this.subtaskForm.startTime,
          endTime: this.subtaskForm.endTime,
          description: this.subtaskForm.description,
          completed: false // ?????????        };

        this.plan.subTasks.push(newSubtask);

        uni.showToast({
          title: '??????',
          icon: 'success'
        });
      }

      // ?????
      this.showSubtaskModal = false;

      // ??????????
      this.updateOriginalPlan();
    },

    // ??????    updateOriginalPlan() {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2]; // ??????
      if (prevPage && prevPage.$vm && prevPage.$vm.plans) {
        const planIndex = prevPage.$vm.plans.findIndex(p => p.id === this.planId);
        if (planIndex !== -1) {
          // ??????          if (this.plan && this.plan.subTasks) {
            prevPage.$vm.plans[planIndex].subTasks = JSON.parse(JSON.stringify(this.plan.subTasks));
          }
        }
      }
    },

    // ????????    getPriorityText(priority) {
      if (priority === 'low') return '????';
      if (priority === 'medium') return '????';
      if (priority === 'high') return '????';
      return '????';
    },

    // ????????
    getPlanTypeText(type) {
      switch (type) {
        case 'daily':
          return '????;
        case 'weekly':
          return '????;
        case 'monthly':
          return '????;
        case 'custom':
          return '??????;
        default:
          return '????;
      }
    },

    // ??????    formatDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      return `${year}??{month}??{day}?`;
    },

    // ????????????????
    isCurrentPlan() {
      if (!this.plan) return false;

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const startDate = new Date(this.plan.date);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(this.plan.endDate || this.plan.date);
      endDate.setHours(23, 59, 59, 999);

      return today >= startDate && today <= endDate;
    },

    // ????
    handleCheckIn() {
      if (!this.plan) return;

      // ????????????????      if (this.plan.todayCompleted) {
        uni.showToast({
          title: '??????,
          icon: 'none'
        });
        return;
      }

      // ????????      uni.showModal({
        title: '????',
        content: `????"${this.plan.title}"?????`,
        success: (res) => {
          if (res.confirm) {
            this.checkInPlan();
          }
        }
      });
    },

    // ??????
    checkInPlan() {
      if (!this.plan) return;

      // ??????      uni.showLoading({
        title: '????..'
      });

      // ??????
      const planData = {
        planId: this.plan.id,
        title: this.plan.title,
        type: this.plan.type,
        date: this.plan.date,
        startDate: this.plan.date,
        endDate: this.plan.endDate || this.plan.date,
        priority: this.plan.priority || 'medium',
        description: this.plan.description || '',
        aiGenerated: this.plan.aiGenerated || false,
        completionTime: new Date().toISOString()
      };

      // ??API???????      planApi.updatePlanStatus(this.plan.id, planData).then(res => {
        if (res.code === 200) {
          // ???????          this.plan.todayCompleted = true;
          this.plan.lastCheckInTime = planData.completionTime;

          // ??????
          uni.showToast({
            title: '????',
            icon: 'success'
          });
        } else {
          console.error('????:', res.message);
          uni.showToast({
            title: res.message || '????',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('????:', err);
        uni.showToast({
          title: '??????????',
          icon: 'none'
        });
      }).finally(() => {
        uni.hideLoading();
      });
    },

    // ????????    formatDateTime(dateTime) {
      if (!dateTime) return '';

      const date = new Date(dateTime);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // ??????????    isPlanCompleted() {
      if (!this.plan) return false;

      // ??????????
      if (this.plan.completed) return true;

      // ???????????
      try {
        const now = new Date();
        now.setHours(0, 0, 0, 0); // ????????????

        // ????????
        const endDate = this.plan.endDate || this.plan.date;
        if (!endDate) return false;

        const planEndDate = new Date(endDate);
        planEndDate.setHours(0, 0, 0, 0);

        // ??????????????????
        return planEndDate < now;
      } catch (error) {
        console.error('??????????:', error);
        return false;
      }
    },

    // ??????    deleteSubtask(index) {
      if (!this.plan || !this.plan.subTasks || index < 0 || index >= this.plan.subTasks.length) return;

      uni.showModal({
        title: '??????,
        content: '????????????,
        success: (res) => {
          if (res.confirm) {
            // ??????            if (this.plan && this.plan.subTasks) {
              this.plan.subTasks.splice(index, 1);
            }

            // ??????????
            this.updateOriginalPlan();

            uni.showToast({
              title: '??????',
              icon: 'success'
            });
          }
        }
      });
    },

    // ????????????    onSubTaskStartTimeChange(e) {
      this.subtaskForm.startTime = e.detail.value;
    },

    // ????????????    onSubTaskEndTimeChange(e) {
      this.subtaskForm.endTime = e.detail.value;
    },

    // ????????????
    timeToMinutes(timeStr) {
      if (!timeStr) return 0;

      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    }
  }
}
</script>

<style>
.plan-detail-container {
  min-height: 800px;
  background-image: linear-gradient(135deg, #eef2ff, #f5f7fa);
  display: flex;
  flex-direction: column;
  padding-bottom: 80px; /* ??????????*/
  position: relative;
}

.detail-container {
  flex: 1;
  padding: 16px;

}


  to { opacity: 1; transform: translateY(0); }
}

.header {
  height: 60px;
  background: rgba(255, 255, 255, 0.9);

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 16px rgba(31, 60, 136, 0.06);
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid rgba(91, 127, 255, 0.08);
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: transform 0.2s ease;
}

.back-button:active {
  transform: scale(0.92);
}

.back-icon {
  font-size: 24px;
  color: #5B7FFF;
  font-weight: normal;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.5px;
}

.header-buttons {
  display: flex;
  align-items: center;
}

.header-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.header-button:active {
  transform: scale(0.92);
}

.header-button__icon {
  font-size: 22px;
  color: #5B7FFF;
}

.plan-detail-content {
  flex: 1;
  padding: 20px;
  padding-bottom: 88px;
}

.plan-detail-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 10px 30px rgba(31, 60, 136, 0.06);
  position: relative;
  overflow: hidden;
}



.plan-detail-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(31, 60, 136, 0.08);
  position: relative;
  overflow: hidden;
  background-image: linear-gradient(to bottom right, rgba(91, 127, 255, 0.05), rgba(128, 184, 245, 0.05));
  border: 1px solid rgba(91, 127, 255, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}



.plan-detail-title {
  font-size: 22px;
  font-weight: 700;
  color: #2E3A59;
  margin-bottom: 14px;
  line-height: 1.3;
  letter-spacing: 0.3px;
}

.plan-detail-badges {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 14px;
  gap: 8px;
}

.plan-badge {
  padding: 5px 12px;
  border-radius: 10px;
  font-size: 12px;
  color: #FFFFFF;
  font-weight: bold;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  height: 24px;
}



.plan-badge--ai {
  background: linear-gradient(135deg, #1890FF, #39BDFF);
}



/* ?? plan-badge--priority ???????????? */

.plan-priority-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  height: 28px;
}

.plan-type-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(91, 127, 255, 0.1);
  color: #5B7FFF;
  border: 1px solid rgba(91, 127, 255, 0.2);
}

.plan-detail-time-range {
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  gap: 4px;
}

.plan-priority-badge--low {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.plan-priority-badge--medium {
  background-color: rgba(250, 173, 20, 0.1);
  color: #FAAD14;
  border: 1px solid rgba(250, 173, 20, 0.2);
}

.plan-priority-badge--high {
  background-color: rgba(255, 77, 79, 0.1);
  color: #FF4D4F;
  border: 1px solid rgba(255, 77, 79, 0.2);
}

.plan-detail-date {
  font-size: 15px;
  color: #5E6C84;
  display: flex;
  align-items: center;
}



.plan-detail-section {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 22px;
  margin-bottom: 20px;
  box-shadow: 0 6px 20px rgba(31, 60, 136, 0.05);
  border: 1px solid rgba(91, 127, 255, 0.08);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
  overflow: hidden;
}

.plan-detail-section:hover {
  box-shadow: 0 8px 24px rgba(31, 60, 136, 0.08);
  transform: translateY(-2px);
}

.plan-detail-description__text {
  font-size: 15px;
  color: #5E6C84;
  line-height: 1.6;
  margin-top: 10px;
  letter-spacing: 0.2px;
}

.subtasks-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
}

.section-title {
  font-size: 17px;
  font-weight: bold;
  color: #2E3A59;
  display: flex;
  align-items: center;
  letter-spacing: 0.3px;
  position: relative;
  padding-bottom: 8px;
}





.resource-section

.section-action {
  font-size: 15px;
  color: #5B7FFF;
  font-weight: bold;
  padding: 6px 12px;
  background-color: rgba(91, 127, 255, 0.1);
  border-radius: 12px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.section-action:active {
  transform: scale(0.95);
  background-color: rgba(91, 127, 255, 0.2);
}

.subtask-list {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
}

.subtask-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 14px;
  background-color: #FFFFFF;
  border-radius: 14px;
  margin-bottom: 10px;
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.04);
  position: relative;
  transition-property: transform, opacity; transition-duration: 0.25s; transition-timing-function: ease;
  height: 60px;
  border: 1px solid rgba(240, 243, 250, 0.8);
}

.subtask-item:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(31, 60, 136, 0.03);
  background-color: #F9FAFC;
}

.subtask-item--completed {
  background-color: rgba(240, 248, 240, 0.8);
  border-color: rgba(76, 175, 80, 0.15);
}

.subtask-checkbox {
  width: 22px;
  height: 22px;
  border-radius: 999px;
  border: 2px solid #80B8F5;
  margin-right: 14px;
  flex-shrink: 0;
  position: relative;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.1);
}

.subtask-checkbox:active {
  transform: scale(0.9);
}

.subtask-checkbox--checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.15);
}




  100% { opacity: 1; transform: rotate(-45deg) scale(1); }
}

.subtask-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* ?????? */
}

.subtask-title {
  font-size: 15px;
  font-weight: bold;
  color: #2E3A59;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.2px;
}

.subtask-title--completed {
  color: #8F9BB3;
  text-decoration: line-through;
  font-weight: 400;
  opacity: 0.8;
}

.subtask-time {
  font-size: 12px;
  color: #5E6C84;
  display: flex;
  align-items: center;
  white-space: nowrap;
  background-color: rgba(91, 127, 255, 0.08);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 8px;
  font-weight: bold;
}



.subtask-description {
  font-size: 12px;
  color: #8F9BB3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  margin-left: 4px;
  font-style: italic;
  opacity: 0.9;
}

.subtask-completion-time {
  font-size: 10px;
  color: #4CAF50;
  margin-top: 4px;
}

.subtask-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.subtask-action-btn {
  width: 32px;
  height: 32px;
  border-radius: 999px;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.subtask-action-btn:active {
  transform: scale(0.92);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
}

.subtask-action-icon {
  font-size: 20px;
  color: #FFFFFF;
  font-weight: normal;
}

.subtask-actions-menu {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.subtask-edit-btn, .subtask-delete-btn {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.subtask-edit-btn {
  background-color: rgba(91, 127, 255, 0.1);
}

.subtask-delete-btn {
  background-color: rgba(255, 77, 79, 0.1);
}

.subtask-edit-btn:active, .subtask-delete-btn:active {
  transform: scale(0.9);
}

.subtask-edit-icon {
  font-size: 12px;
  color: #5B7FFF;
}

.subtask-delete-icon {
  font-size: 14px;
  color: #FF4D4F;
}

/* ?????????? */
.plan-progress-container {
  margin-top: 12px;
  background-color: #F9FAFC;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.plan-progress-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.plan-progress-title {
  font-size: 15px;
  font-weight: bold;
  color: #2E3A59;
}

.plan-progress-stats {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.plan-progress-stats-text {
  font-size: 13px;
  color: #5E6C84;
  background-color: rgba(91, 127, 255, 0.08);
  padding: 4px 10px;
  border-radius: 12px;
}

/* ???????? */
.resource-list {
  display: flex;
  flex-direction: column;
  margin-top: 12px;
}

.resource-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px;
  background-color: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 8px;
  box-shadow: 0 4px 10px rgba(31, 60, 136, 0.04);
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.resource-item:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(31, 60, 136, 0.03);
}

.resource-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 8px rgba(91, 127, 255, 0.15);
  flex-shrink: 0;
}

.resource-icon--course {
  background: linear-gradient(135deg, #1890FF, #39BDFF);
}

.resource-icon--video {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
}

.resource-icon--article {
  background: linear-gradient(135deg, #52C41A, #73D13D);
}

.resource-icon--tool {
  background: linear-gradient(135deg, #FAAD14, #FFC53D);
}

.resource-icon--audio {
  background: linear-gradient(135deg, #722ED1, #B37FEB);
}

.resource-icon__text {
  font-size: 18px;
  color: #FFFFFF;
}

.resource-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.resource-title {
  font-size: 14px;
  font-weight: bold;
  color: #2E3A59;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resource-desc {
  font-size: 11px;
  color: #5E6C84;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resource-meta {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.resource-difficulty, .resource-relevance {
  font-size: 10px;
  color: #8F9BB3;
  background-color: #F5F7FA;
  padding: 1px 6px;
  border-radius: 8px;
  margin-right: 8px;
}

.resource-arrow {
  font-size: 18px;
  color: #C5CEE0;
  margin-left: 4px;
}

.empty-resources {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  background-color: #F8F9FA;
  border-radius: 12px;
  border: 1px dashed #E0E0E0;
  margin-top: 16px;
}

.empty-resources__text {
  font-size: 14px;
  color: #8F9BB3;
  text-align: center;
  line-height: 1.5;
}

.ai-recommend-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6px 10px;
  background-color: #F0F8FF;
  border-radius: 12px;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.ai-recommend-btn:active {
  transform: scale(0.96);
  background-color: #E0F0FF;
}

.ai-recommend-btn--loading {
  background-color: #F5F7FA;
  opacity: 0.8;
}

.ai-recommend-icon {
  font-size: 14px;
  margin-right: 4px;
}

.ai-recommend-text {
  font-size: 12px;
  color: #5B7FFF;
}

.footer {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);

  border-top: 1px solid rgba(91, 127, 255, 0.1);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  box-shadow: 0 -4px 16px rgba(31, 60, 136, 0.08);

}


  to { transform: translateY(0); }
}

.footer-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  max-width: 500px;
  gap: 10px;
}

.btn-cancel, .btn-primary, .btn-edit, .btn-delete {
  padding: 10px 0;
  border-radius: 12px;
  font-size: 15px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.25s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex: 1;
  height: 44px;
  position: relative;
  overflow: hidden;
}

.btn-cancel {
  background-color: #F5F7FA;
  color: #5E6C84;
  border: 1px solid rgba(94, 108, 132, 0.1);
}

.btn-cancel:active {
  background-color: #EBEDF0;
}

.btn-primary {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
}



.btn-primary:

.btn-edit {
  background: linear-gradient(135deg, #F79F77, #F7B977);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(247, 159, 119, 0.2);
}



.btn-edit:

.btn-delete {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.2);
}



.btn-delete:

.btn-hover {
  opacity: 0.9;
  transform: translateY(1px);
}

.footer-button__text {
  font-size: 17px;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

/* ?????? */
.modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(23, 43, 77, 0.6);


}


  to { opacity: 1; }
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 420px;
  background-color: #FFFFFF;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(31, 60, 136, 0.2);
  animation: slide-up 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}


  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(to right, #5B7FFF, #80B8F5);
  color: #FFFFFF;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
}

.modal-close {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #FFFFFF;
  opacity: 0.8;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
  position: absolute;
  right: 15px;
  top: 15px;
}

.modal-close:active {
  transform: scale(0.92);
  opacity: 1;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-top: 1px solid #F0F3FA;
}

.btn-cancel, .btn-primary {
  padding: 10px 0;
  border-radius: 12px;
  font-size: 15px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.25s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex: 1;
  height: 44px;
  position: relative;
  overflow: hidden;
  margin: 0 5px;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: bold;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.modal-btn:active {
  transform: scale(0.96);
}

.modal-btn--cancel {
  color: #5E6C84;
  background-color: #F5F7FA;
  margin-right: 12px;
}

.modal-btn--confirm {
  color: #FFFFFF;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  font-size: 15px;
  color: #2E3A59;
  margin-bottom: 8px;
  display: flex;
  font-weight: bold;
}



.form-input {
  width: 100%;
  height: 48px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #2E3A59;
  background-color: #F9FAFC;
}

.form-textarea {
  width: 100%;
  height: 100px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  color: #2E3A59;
  background-color: #F9FAFC;
}

.time-range-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.picker-item {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  font-size: 15px;
  color: #2E3A59;
  background-color: #F9FAFC;
  margin: 0 4px;
}

.picker-item:first-child {
  margin-left: 0;
}

.picker-item:last-child {
  margin-right: 0;
}

.picker-separator {
  padding: 0 8px;
  color: #5E6C84;
}

/* ?????? */
.check-in-section {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.check-in-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  border-radius: 24px;
  padding: 6px 20px;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  width: 70%;
  height: 36px;
}

.check-in-button--completed {
  background: linear-gradient(135deg, #52C41A, #7ED321);
}

.check-in-icon {
  font-size: 16px;
  margin-right: 6px;
  color: #FFFFFF;
  line-height: 1;
}

.check-in-text {
  font-size: 14px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 1;
}

.last-check-in-time {
  margin-top: 8px;
  font-size: 12px;
  color: #8C8C8C;
}
</style>

