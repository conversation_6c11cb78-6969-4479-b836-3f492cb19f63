$files = Get-ChildItem -Path "app/sla/pages" -Filter "*.uvue" -Recurse

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    
    # 检查文件是否包含<template>标签但缺少</template>结束标签
    if ($content -match "<template>" -and $content -notmatch "</template>") {
        Write-Host "修复文件: $($file.FullName)"
        
        # 检查文件是否以</style>结尾
        if ($content -match "</style>\s*$") {
            $newContent = $content -replace "</style>\s*$", "</style>`r`n</template>"
            Set-Content -Path $file.FullName -Value $newContent
            Write-Host "已添加</template>结束标签"
        } else {
            Write-Host "文件格式不符合预期，需要手动检查"
        }
    }
}

Write-Host "修复完成!" 