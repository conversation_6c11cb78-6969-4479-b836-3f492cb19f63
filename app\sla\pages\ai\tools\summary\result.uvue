<template>
  <view class="result-container">
    <!-- 顶部导航栏 -->
    <view class="result-header">
      <view class="result-back" @click="goBack">
        <text class="result-back__icon">←</text>
      </view>
      <text class="result-title">总结结果</text>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="result-content" scroll-y>
      <!-- 总结内容卡片 -->
      <view class="result-card">
        <view class="result-card__header">
          <text class="result-card__title">文档总结</text>
          <view class="result-card__actions">
            <view class="result-action" @click="copyToClipboard('summary')">
              <text class="result-action__text">复制</text>
            </view>
          </view>
        </view>

        <!-- Markdown内容 -->
        <view class="markdown-content">
          <rich-text :nodes="formattedSummary"></rich-text>
        </view>
      </view>

      <!-- 关键词卡片 -->
      <view class="keywords-card" v-if="keywords && keywords.length > 0">
        <view class="keywords-card__header">
          <text class="keywords-card__title">关键词</text>
          <view class="keywords-card__actions">
            <view class="keywords-action" @click="copyToClipboard('keywords')">
              <text class="keywords-action__text">复制</text>
            </view>
          </view>
        </view>

        <view class="keywords-content">
          <view class="keywords-list">
            <text
              v-for="(keyword, index) in keywords"
              :key="index"
              class="keyword-tag"
              :style="{ backgroundColor: getKeywordColor(index), color: getKeywordTextColor(index) }"
            >{{ keyword }}</text>
          </view>
        </view>
      </view>

      <!-- 思维导图卡片 -->
      <view class="mindmap-card" v-if="generateMindmap">
        <view class="mindmap-card__header">
          <text class="mindmap-card__title">思维导图</text>
          <view class="mindmap-card__actions">
            <view class="mindmap-action" @click="copyToClipboard('mindmap')">
              <text class="mindmap-action__text">复制链接</text>
            </view>
          </view>
        </view>

        <view class="mindmap-preview">
          <image class="mindmap-image" src="/static/images/mindmap-preview.svg" mode="aspectFit"></image>
          <view class="mindmap-link">
            <text class="mindmap-link__text">{{ mindmapUrl }}</text>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="bottom-actions">
        <view class="bottom-action bottom-action--secondary" @click="saveSummary">
          <text class="bottom-action__text">保存总结</text>
        </view>
        <view class="bottom-action bottom-action--primary" @click="createNewSummary">
          <text class="bottom-action__text">创建新总结</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { showSuccess, showError } from '../../../../utils/notificationManager.js';
import { generateSummaryFromText } from '../../../../utils/api/aitool.js';

export default {
  data() {
    return {
      summaryId: '',
      summary: '',
      keywords: [] as string[],
      generateMindmap: false,
      mindmapUrl: '',
      formattedSummary: '',
      title: '',
      originalFileName: '',
      showMindmap: false,
      currentTabIndex: 0,
      tabContentHeight: 400,
      isSaving: false,
      isSharing: false,
      fontSize: 16
    }
  },
  computed: {
    formattedTitle() {
      return this.title || '文档总结';
    },
    formattedKeywords() {
      return this.keywords.length > 0 ? this.keywords.join('、') : '无关键词';
    },
    formattedOriginalFileName() {
      return this.originalFileName || '手动输入';
    }
  },
  mounted() {
    this.loadSummaryData();
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.redirectTo({
            url: '/pages/ai/tools/summary/index'
          });
        }
      });
    },
    formatMarkdown() {
      try {
        let formatted = this.summary;

        formatted = formatted.replace(/\n\n/g, '</p><p>');
        formatted = '<p>' + formatted + '</p>';

        formatted = formatted.replace(/\n/g, '<br>');

        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

        this.formattedSummary = formatted;
      } catch (error) {
        console.error('格式化Markdown内容出错:', error);
        this.formattedSummary = this.summary.replace(/\n/g, '<br>');
      }
    },
    copyToClipboard(type) {
      let content = '';
      let successMsg = '';

      if (type === 'summary') {
        content = this.summary;
        successMsg = '总结内容已复制到剪贴板';
      } else if (type === 'mindmap') {
        content = this.mindmapUrl;
        successMsg = '思维导图链接已复制到剪贴板';
      } else if (type === 'keywords') {
        content = this.keywords.join('、');
        successMsg = '关键词已复制到剪贴板';
      }

      uni.setClipboardData({
        data: content,
        success: () => {
          uni.showToast({
            title: successMsg,
            icon: 'success'
          });
        }
      });
    },
    getKeywordColor(index) {
      const colors = [
        'rgba(74, 123, 219, 0.1)',   // 蓝色
        'rgba(60, 179, 113, 0.1)',   // 绿色
        'rgba(255, 165, 0, 0.1)',    // 橙色
        'rgba(238, 130, 238, 0.1)',  // 紫色
        'rgba(106, 90, 205, 0.1)',   // 紫罗兰
        'rgba(0, 191, 255, 0.1)',    // 深天蓝
        'rgba(255, 99, 71, 0.1)',    // 番茄
        'rgba(46, 139, 87, 0.1)'     // 海绿色
      ];

      return colors[index % colors.length];
    },
    getKeywordTextColor(index) {
      const colors = [
        '#4A7BDB',   // 蓝色
        '#3CB371',   // 绿色
        '#FF8C00',   // 橙色
        '#9370DB',   // 紫色
        '#6A5ACD',   // 紫罗兰
        '#00BFFF',   // 深天蓝
        '#FF6347',   // 番茄
        '#2E8B57'    // 海绿色
      ];

      return colors[index % colors.length];
    },
    extractKeywordsFromSummary(summary) {
      if (!summary) return [];

      const sentences = summary.split(/[。！？.!?]/);

      const keywords: string[] = [];
      const stopWords = ['的', '了', '是', ' ', '在', '有', '和', '与', '为', '这', '那', '它', '们', '我', '你', '他', '她', '而', '就', '也', '但', '或', '如', '等', '等等', '中', '对', '从', '向等'];

      sentences.forEach(sentence => {
        const cleanSentence = sentence.trim();
        if (cleanSentence.length < 3) return;

        const words = cleanSentence.split(/\s+/).filter(w => w.length > 1);

        if (words.length > 0) {
          const filteredWords = words.filter(w => !stopWords.includes(w));

          if (filteredWords.length > 0) {
            const keyword = filteredWords[0];
            if (keyword && keyword.length > 1 && keyword.length <= 5 && !keywords.includes(keyword)) {
              keywords.push(keyword);
            }
          }
        }
      });

      return keywords.slice(0, 5);
    },
    generateDefaultKeywords(summary) {
      if (!summary) return [];

      const defaultKeywords = [
        'AI文档总结',
        'AI助手',
        '智能摘要',
        'AI分析',
        'AI学习',
        'AI工具',
        'AI辅助',
        'AI智能助手'
      ];

      const selectedKeywords: string[] = [];
      defaultKeywords.forEach(keyword => {
        if (summary.includes(keyword) ||
            summary.includes(keyword.substring(0, 2))) {
          selectedKeywords.push(keyword);
        }
      });

      if (selectedKeywords.length < 3) {
        const remainingKeywords = defaultKeywords.filter(k => !selectedKeywords.includes(k));
        const neededCount = Math.min(5 - selectedKeywords.length, remainingKeywords.length);
        selectedKeywords.push(...remainingKeywords.slice(0, neededCount));
      }

      return selectedKeywords.slice(0, 5);
    },
    saveSummary() {
      if (this.summaryId) {
        showSuccess('已保存');
        return;
      }

      const data = {
        text: this.summary,
        generateMindmap: this.generateMindmap,
        title: this.title || '文档总结'
      };

      generateSummaryFromText(data)
        .then(response => {
          console.log('保存总结响应:', response);

          if (response.statusCode === 503) {
            console.error('服务暂时不可用:', response.statusCode);
            throw new Error('服务繁忙');
          }

          if (response && response.data && response.data.code === 200 && response.data.data) {
            this.summaryId = response.data.data.summaryId;

            showSuccess('保存成功');
          } else {
            throw new Error((response.data && response.data.message) || '保存失败');
          }
        })
        .catch(err => {
          console.error('保存总结失败:', err);
          showError('保存失败', err.message || '请稍后重试');
        });
    },
    createNewSummary() {
      uni.navigateBack({
        success: () => {
          const pages = getCurrentPages();
          if (pages.length >= 2) {
            const prevPage = pages[pages.length - 2];
            if (prevPage && prevPage.$vm) {
              prevPage.$vm.clearCache && prevPage.$vm.clearCache();
            }
          }
        }
      });
    },
    loadSummaryData() {
      const eventChannel = this.getOpenerEventChannel();
      
      if (eventChannel) {
        eventChannel.on('acceptSummaryData', (data) => {
          console.log('从事件通道接收到数据:', data);
          
          if (data) {
            this.processSummaryData(data);
          } else {
            console.error('接收到的数据无效');
            
            showError('数据错误', '接收到的总结数据无效');
            
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        });
      }
      
      const query = this.$route?.query;
      if (query && query.id) {
        this.summaryId = query.id as string;
        
        this.loadSummaryFromHistory(this.summaryId);
      }
    },
    loadSummaryFromHistory(id) {
      if (!id) return;
      
      try {
        const history = uni.getStorageSync('summary_history') || [];
        
        const record = history.find(item => item.id === id);
        
        if (record) {
          this.processSummaryData(record);
        } else {
          console.error('未找到匹配的历史记录:', id);
          
          showError('数据错误', '未找到匹配的总结记录');
        }
      } catch (error) {
        console.error('加载历史记录失败:', error);
        
        showError('数据错误', '加载总结历史失败');
      }
    },
    processSummaryData(data) {
      this.summary = data.summary || '';
      
      this.summaryId = data.id || data.summaryId || '';
      
      this.title = data.title || '文档总结';
      
      this.originalFileName = data.originalFileName || '';
      
      this.generateMindmap = !!data.generateMindmap;
      
      this.mindmapUrl = data.mindmapUrl || '';
      
      if (data.keywords && Array.isArray(data.keywords) && data.keywords.length > 0) {
        this.keywords = data.keywords as string[];
      } else {
        this.keywords = this.extractKeywordsFromSummary(this.summary);
      }
      
      // 格式化Markdown内容
      this.formatMarkdown();
    },
    switchTab(index) {
      this.currentTabIndex = index;
    },
    increaseFontSize() {
      if (this.fontSize < 24) {
        this.fontSize += 2;
      }
    },
    decreaseFontSize() {
      if (this.fontSize > 12) {
        this.fontSize -= 2;
      }
    },
    shareSummary() {
      this.isSharing = true;
      
      uni.showShareMenu({
        withShareTicket: true,
        success: () => {
          this.isSharing = false;
        },
        fail: (err) => {
          console.error('分享失败:', err);
          this.isSharing = false;
          
          showError('分享失败', '请检查应用权限设置');
        }
      });
    },
    copySummary() {
      uni.setClipboardData({
        data: this.summary,
        success: () => {
          showSuccess('复制成功', '总结内容已复制到剪贴板');
        },
        fail: () => {
          showError('复制失败', '请检查应用权限设置');
        }
      });
    }
  }
}
</script>

<style>
.result-container {
  min-height: 800px;
  background-color: #F8F9FA;
  display: flex;
  flex-direction: column;
}

.result-header {
  height: 56px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  display: flex;
  align-items: center;
  justify-content: center; /* 居中 */
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(74, 123, 219, 0.2);
  position: relative;
}

.result-back {
  position: absolute;
  left: 16px;
  top: 0;
  height: 56px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center; /* 居中 */
  z-index: 1; /* 提高层级 */
}

.result-back__icon {
  font-size: 20px; /* 调整大小 */
  color: #FFFFFF;
  font-weight: bold;
}

.result-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
  width: 100%; /* 占满宽度 */
  position: absolute; /* 绝对定位确保居中 */
  left: 0;
}

.result-content {
  flex: 1;
  padding: 16px;
}

/* 总结内容卡片 */
.result-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-card__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #F0F0F0;
}

.result-card__title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.result-card__actions {
  display: flex;
  flex-direction: row;
}

.result-action {
  padding: 6px 12px;
  border-radius: 16px;
  background-color: #F0F0F0;
}

.result-action__text {
  font-size: 14px;
  color: #666666;
}

/* Markdown内容 */
.markdown-content {
  margin-bottom: 16px;
  line-height: 1.6;
}

/* 关键词卡片 */
.keywords-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.keywords-card__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #F0F0F0;
}

.keywords-card__title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.keywords-card__actions {
  display: flex;
  flex-direction: row;
}

.keywords-action {
  padding: 6px 12px;
  border-radius: 16px;
  background-color: #F0F0F0;
}

.keywords-action__text {
  font-size: 14px;
  color: #666666;
}

.keywords-content {
  margin-bottom: 16px;
}

.keywords-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: -4px;
  justify-content: flex-start;
}

.keyword-tag {
  margin: 4px;
  padding: 6px 12px;
  background-color: rgba(74, 123, 219, 0.1);
  border-radius: 16px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  color: #4A7BDB;
  font-weight: bold;
  display: flex;
}

/* 思维导图卡片 */
.mindmap-card {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mindmap-card__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #F0F0F0;
}

.mindmap-card__title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.mindmap-card__actions {
  display: flex;
  flex-direction: row;
}

.mindmap-action {
  padding: 6px 12px;
  border-radius: 16px;
  background-color: #F0F0F0;
}

.mindmap-action__text {
  font-size: 14px;
  color: #666666;
}

.mindmap-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mindmap-image {
  width: 100%;
  height: 200px;
  margin-bottom: 12px;
  border-radius: 8px;
  background-color: #F0F0F0;
}

.mindmap-link {
  width: 100%;
  padding: 12px;
  background-color: #F8F9FA;
  border-radius: 8px;
  margin-bottom: 8px;
}

.mindmap-link__text {
  font-size: 14px;
  color: #4A7BDB;
  word-break: break-all;
}

/* 底部按钮 */
.bottom-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 24px;
  margin-bottom: 24px;
}

.bottom-action {
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32px;
  flex: 1;
  margin: 0 8px;
}

.bottom-action--primary {
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  box-shadow: 0 4px 12px rgba(74, 123, 219, 0.3);
}

.bottom-action--secondary {
  background-color: #F0F4FF;
  border: 1px solid #4A7BDB;
}

.bottom-action--primary .bottom-action__text {
  font-size: 16px;
  font-weight: bold;
  color: #FFFFFF;
}

.bottom-action--secondary .bottom-action__text {
  font-size: 16px;
  font-weight: bold;
  color: #4A7BDB;
}
</style>
</template>

