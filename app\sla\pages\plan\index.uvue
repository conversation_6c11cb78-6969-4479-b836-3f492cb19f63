<template>
  <view class="plan-container">
    <!-- 顶部导航栏 -->
    <sla-navbar title="学习计划" :show-back="false">
      <template #right>
        <view class="history-button" @click="navigateToHistory">
          <text class="history-button__text">历史</text>
        </view>
      </template>
    </sla-navbar>

    <!-- 主要内容 -->
    <scroll-view class="plan-content" scroll-y>
      <!-- 统计数据区域 -->
      <view class="plan-stats">
        <view class="stat-item">
          <text class="stat-value">{{planStats.totalPlans}}</text>
          <text class="stat-label">总计划</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{planStats.inProgressPlans || planStats.totalPlans - planStats.completedPlans}}</text>
          <text class="stat-label">进行中</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{planStats.completedPlans}}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{planStats.checkInCount || 0}}</text>
          <text class="stat-label">今日打卡</text>
        </view>
      </view>

      <!-- 计划标签页 -->
      <view class="plan-tabs">
        <view
          class="plan-tab"
          :class="{'plan-tab--active': activeTab === 'today'}"
          @click="switchTab('today')"
        >
          <text class="plan-tab__text">今日计划</text>
        </view>
        <view
          class="plan-tab"
          :class="{'plan-tab--active': activeTab === 'upcoming'}"
          @click="switchTab('upcoming')"
        >
          <text class="plan-tab__text">即将到来</text>
        </view>
        <view
          class="plan-tab"
          :class="{'plan-tab--active': activeTab === 'completed'}"
          @click="switchTab('completed')"
        >
          <text class="plan-tab__text">已完成</text>
        </view>
      </view>

      <!-- 计划列表区域 -->
      <view class="plan-list-container">
        <!-- 今日计划 -->
        <view class="plan-section" v-if="activeTab === 'today'">
          <view class="plan-list">
            <view v-if="todayPlans.length === 0" class="empty-state">
              <view class="empty-icon">📋</view>
              <text class="empty-title">暂无今日计划</text>
              <text class="empty-text">点击右下角"+"按钮添加计划</text>
            </view>
            <block v-else>
              <view
                v-for="(plan, index) in todayPlans"
                :key="plan.planId || index"
                class="plan-row"
                :class="{'plan-row--completed': plan.todayCompleted}"
                @click="viewPlanDetail(plan)"
              >
                <view
                  class="plan-row__checkbox"
                  :class="{'plan-row__checkbox--checked': plan.todayCompleted}"
                  @click.stop="plan.todayCompleted ? null : togglePlanStatus(plan)"
                >
                  <text class="plan-row__check-icon" v-if="plan.todayCompleted">✓</text>
                </view>
                <view class="plan-row__body">
                  <view class="plan-row__header">
                    <text class="plan-row__title" :class="{'plan-row__title--completed': plan.todayCompleted}">{{ plan.title }}</text>
                    <view class="plan-row__tags">
                      <text class="plan-row__tag" :class="'plan-row__tag--' + plan.priority">{{ getPriorityText(plan.priority) }}</text>
                      <text class="plan-row__tag plan-row__tag--type">{{ getPlanTypeText(plan.type) }}</text>
                    </view>
                  </view>
                  <view class="plan-row__info">
                    <text class="plan-row__date">{{ formatDate(plan.date) }}</text>
                    <text class="plan-row__time" v-if="getTimeRange(plan.subTasks)">{{ getTimeRange(plan.subTasks) }}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>

        <!-- 即将到来 -->
        <view class="plan-section" v-if="activeTab === 'upcoming'">
          <view class="plan-list">
            <view v-if="upcomingPlans.length === 0" class="empty-state">
              <view class="empty-icon">📅</view>
              <text class="empty-title">暂无即将到来的计划</text>
              <text class="empty-text">点击右下角"+"按钮添加计划</text>
            </view>
            <block v-else>
              <view
                v-for="(plan, index) in upcomingPlans"
                :key="plan.planId || index"
                class="plan-row"
                @click="viewPlanDetail(plan)"
              >
                <view
                  class="plan-row__checkbox"
                  :class="{'plan-row__checkbox--disabled': true}"
                >
                  <!-- 即将到来的计划不能打卡 -->
                </view>
                <view class="plan-row__body">
                  <view class="plan-row__header">
                    <text class="plan-row__title">{{ plan.title }}</text>
                    <view class="plan-row__tags">
                      <text class="plan-row__tag" :class="'plan-row__tag--' + plan.priority">{{ getPriorityText(plan.priority) }}</text>
                      <text class="plan-row__tag plan-row__tag--type">{{ getPlanTypeText(plan.type) }}</text>
                    </view>
                  </view>
                  <view class="plan-row__info">
                    <text class="plan-row__date">{{ formatDate(plan.date) }}</text>
                    <text class="plan-row__time" v-if="getTimeRange(plan.subTasks)">{{ getTimeRange(plan.subTasks) }}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>

        <!-- 已完成计划 -->
        <view class="plan-section" v-if="activeTab === 'completed'">
          <view class="plan-list">
            <view v-if="completedPlans.length === 0" class="empty-state">
              <view class="empty-icon">✅</view>
              <text class="empty-title">暂无已完成的计划</text>
              <text class="empty-text">完成计划后会在这里显示</text>
            </view>
            <block v-else>
              <view
                v-for="(plan, index) in completedPlans"
                :key="plan.planId || index"
                class="plan-row"
                :class="{'plan-row--completed': true}"
                @click="viewPlanDetail(plan)"
              >
                <view
                  class="plan-row__checkbox"
                  :class="{'plan-row__checkbox--checked': true}"
                >
                  <text class="plan-row__check-icon">✓</text>
                </view>
                <view class="plan-row__body">
                  <view class="plan-row__header">
                    <text class="plan-row__title" :class="{'plan-row__title--completed': true}">{{ plan.title }}</text>
                    <view class="plan-row__tags">
                      <text class="plan-row__tag" :class="'plan-row__tag--' + plan.priority">{{ getPriorityText(plan.priority) }}</text>
                      <text class="plan-row__tag plan-row__tag--type">{{ getPlanTypeText(plan.type) }}</text>
                    </view>
                  </view>
                  <view class="plan-row__info">
                    <text class="plan-row__date">{{ formatDate(plan.date) }}</text>
                    <text class="plan-row__time" v-if="getTimeRange(plan.subTasks)">{{ getTimeRange(plan.subTasks) }}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 浮动添加按钮 -->
    <view class="floating-button" @click="toggleAddMenu">
      <text class="floating-button__icon">+</text>
    </view>

    <!-- 添加菜单 -->
    <view class="add-menu" v-if="showAddMenu">
      <view class="add-menu-backdrop" @click="toggleAddMenu"></view>
      <view class="add-menu-content">
        <view class="add-menu-item" @click="handleAddPlan">
          <text class="add-menu-item__icon">+</text>
          <text class="add-menu-item__text">手动创建</text>
        </view>
        <view class="add-menu-item" @click="handleAiPlan">
          <text class="add-menu-item__icon">🤖</text>
          <text class="add-menu-item__text">AI生成</text>
        </view>
      </view>
    </view>

    <!-- ???????? -->
    <custom-tab-bar
      :current="1"
      :showAiToolbox="false"
      @centerClick="navigateToAI"
      @tabClick="handleTabClick"
    ></custom-tab-bar>

    <!-- ???????? -->
    <uni-popup ref="addPlanPopup" type="center">
      <view class="plan-modal">
        <view class="plan-modal__header">
          <text class="plan-modal__title">??????</text>
          <text class="plan-modal__close" @click="closeAddPlanModal">?</text>
        </view>

        <view class="plan-modal__body">
          <view class="form-item">
            <text class="form-label required">????</text>
            <input class="form-input" v-model="planForm.title" placeholder="???????" />
          </view>

          <view class="form-item">
            <text class="form-label required">????</text>
            <view class="plan-type-selector">
              <view
                class="plan-type-item"
                :class="{'plan-type-item--active': planForm.type === 'daily'}"
                @click="planForm.type = 'daily'"
              >
                <text class="plan-type-item__text">???</text>
              </view>
              <view
                class="plan-type-item"
                :class="{'plan-type-item--active': planForm.type === 'weekly'}"
                @click="planForm.type = 'weekly'"
              >
                <text class="plan-type-item__text">???</text>
              </view>
              <view
                class="plan-type-item"
                :class="{'plan-type-item--active': planForm.type === 'monthly'}"
                @click="planForm.type = 'monthly'"
              >
                <text class="plan-type-item__text">???</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label required">??</text>
            <picker mode="date" :value="planForm.date" @change="onPlanDateChange">
              <view class="picker-item">{{planForm.date || '?????'}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">???</text>
            <view class="priority-selector">
              <view
                class="priority-item"
                :class="{'priority-item--active': planForm.priority === 'low'}"
                @click="planForm.priority = 'low'"
              >
                <text class="priority-item__text">?</text>
              </view>
              <view
                class="priority-item priority-item--medium"
                :class="{'priority-item--active': planForm.priority === 'medium'}"
                @click="planForm.priority = 'medium'"
              >
                <text class="priority-item__text">?</text>
              </view>
              <view
                class="priority-item priority-item--high"
                :class="{'priority-item--active': planForm.priority === 'high'}"
                @click="planForm.priority = 'high'"
              >
                <text class="priority-item__text">?</text>
              </view>
            </view>
          </view>



          <view class="form-item">
            <text class="form-label">??</text>
            <textarea class="form-textarea" v-model="planForm.description" placeholder="??????????"></textarea>
          </view>
        </view>

        <view class="plan-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeAddPlanModal">??</view>
          <view class="btn-primary" hover-class="btn-hover" @click="savePlanForm">??</view>
        </view>
      </view>
    </uni-popup>

    <!-- AI?????? -->
    <uni-popup ref="aiPlanPopup" type="center">
      <view class="plan-modal">
        <view class="plan-modal__header ai-header">
          <text class="plan-modal__title">AI??????</text>
          <text class="plan-modal__close" @click="closeAiPlanModal">?</text>
        </view>

        <view class="plan-modal__body">
          <view class="ai-intro">
            <text class="ai-intro__text">AI??????????????????????????</text>
            <text class="ai-intro__note">??AI???????3-5????</text>
          </view>

          <view class="form-item">
            <text class="form-label required">????</text>
            <textarea class="form-textarea" v-model="aiPlanForm.description" placeholder="???????????????????????????"></textarea>
          </view>

          <view class="form-item">
            <text class="form-label required">????</text>
            <picker mode="date" :value="aiPlanForm.startDate" @change="onStartDateChange">
              <view class="picker-item">{{aiPlanForm.startDate || '?????'}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">????</text>
            <view class="learning-style-selector">
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'visual'}"
                @click="selectLearningStyle('visual')"
              >
                <text class="learning-style-item__icon">???</text>
                <text class="learning-style-item__text">???</text>
              </view>
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'auditory'}"
                @click="selectLearningStyle('auditory')"
              >
                <text class="learning-style-item__icon">??</text>
                <text class="learning-style-item__text">???</text>
              </view>
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'reading'}"
                @click="selectLearningStyle('reading')"
              >
                <text class="learning-style-item__icon">??</text>
                <text class="learning-style-item__text">???</text>
              </view>
              <view
                class="learning-style-item"
                :class="{'learning-style-item--active': aiPlanForm.learningStyle === 'kinesthetic'}"
                @click="selectLearningStyle('kinesthetic')"
              >
                <text class="learning-style-item__icon">??</text>
                <text class="learning-style-item__text">???</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">????</text>
            <view class="skill-level-selector">
              <text class="skill-level-label">???</text>
              <slider min="1" max="5" :value="aiPlanForm.skillLevel" show-value @change="onSkillLevelChange"></slider>
              <text class="skill-level-label">??</text>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">??????</text>
            <picker :range="studyTimeOptions" :value="studyTimeIndex" @change="onStudyTimeChange">
              <view class="picker-item">{{studyTimeOptions[studyTimeIndex]}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">????</text>
            <textarea class="form-textarea" v-model="aiPlanForm.otherRequirements" placeholder="?????????"></textarea>
          </view>
        </view>

        <view class="plan-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeAiPlanModal">??</view>
          <view class="btn-generate" hover-class="btn-hover" @click="generateAiPlan" :class="{'btn-disabled': isGeneratingPlan}">
            <text v-if="isGeneratingPlan">???...</text>
            <text v-else>????</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- ?????? -->
    <uni-popup ref="planDetailPopup" type="center">
      <view class="plan-detail-modal" v-if="selectedPlan">
        <view class="plan-detail-modal__header">
          <text class="plan-detail-modal__title">????</text>
          <text class="plan-detail-modal__close" @click="closePlanDetailModal">?</text>
        </view>

        <scroll-view class="plan-detail-modal__body" scroll-y>
          <view class="plan-detail-header">
            <text class="plan-detail-title">{{selectedPlan.title}}</text>
            <text class="plan-detail-time">{{selectedPlan.startTime}} - {{selectedPlan.endTime}}</text>
            <text class="plan-detail-date" v-if="selectedPlan.date">{{formatDate(selectedPlan.date)}}</text>
            <text class="plan-detail-type" v-if="selectedPlan.aiGenerated">AI??</text>
          </view>

          <view class="plan-detail-description" v-if="selectedPlan.description">
            <text class="section-title">????</text>
            <text class="plan-detail-description__text">{{selectedPlan.description}}</text>
          </view>

          <view class="plan-detail-subtasks" v-if="selectedPlan.subTasks && selectedPlan.subTasks.length > 0">
            <text class="section-title">???</text>
            <view class="subtask-item" v-for="(subtask, index) in selectedPlan.subTasks" :key="index">
              <view class="subtask-checkbox" :class="{'subtask-checkbox--checked': subtask.completed}" @click="toggleSubtaskStatus(index)"></view>
              <view class="subtask-content">
                <text class="subtask-title" :class="{'subtask-title--completed': subtask.completed}">{{subtask.title}}</text>
                <text class="subtask-time" v-if="subtask.startTime && subtask.endTime">{{subtask.startTime}} - {{subtask.endTime}}</text>
                <text class="subtask-description" v-if="subtask.description">{{subtask.description}}</text>
                <text class="subtask-completion" v-if="subtask.completed">????? {{formatDateTime(subtask.completionTime)}}</text>
              </view>
            </view>
          </view>

          <view class="resource-recommendations" v-if="selectedPlan.recommendedResources && selectedPlan.recommendedResources.length > 0">
            <text class="section-title">????</text>
            <view class="resource-list">
              <view class="resource-item" v-for="(resource, index) in selectedPlan.recommendedResources" :key="index" @click="openResource(resource)">
                <view class="resource-icon" :class="'resource-icon--' + resource.type">
                  <text class="resource-icon__text">{{getResourceTypeIcon(resource.type)}}</text>
                </view>
                <view class="resource-content">
                  <text class="resource-title">{{resource.title}}</text>
                  <text class="resource-desc">{{resource.description}}</text>
                  <view class="resource-meta">
                    <text class="resource-difficulty">??: {{resource.difficulty}}/5</text>
                    <text class="resource-relevance">???? {{resource.relevance}}/5</text>
                  </view>
                </view>
                <text class="resource-arrow">></text>
              </view>
            </view>
          </view>
        </scroll-view>

        <view class="plan-detail-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closePlanDetailModal">??</view>
          <view class="btn-edit" hover-class="btn-hover" @click="showEditPlanModal" v-if="!selectedPlan.aiGenerated && selectedPlan.saved">??</view>
          <view class="btn-primary" hover-class="btn-hover" @click="savePlan" v-if="!selectedPlan.saved">????</view>
        </view>
      </view>
    </uni-popup>

    <!-- ?????? -->
    <uni-popup ref="editPlanPopup" type="center">
      <view class="plan-modal">
        <view class="plan-modal__header">
          <text class="plan-modal__title">????</text>
          <text class="plan-modal__close" @click="closeEditPlanModal">?</text>
        </view>

        <view class="plan-modal__body">
          <view class="form-item">
            <text class="form-label required">????</text>
            <input class="form-input" v-model="editPlanForm.title" placeholder="???????" />
          </view>

          <view class="form-item">
            <text class="form-label">????</text>
            <view class="plan-type-selector">
              <view
                class="plan-type-item plan-type-item--disabled"
                :class="{'plan-type-item--active': editPlanForm.type === 'daily'}"
              >
                <text class="plan-type-item__text">???</text>
              </view>
              <view
                class="plan-type-item plan-type-item--disabled"
                :class="{'plan-type-item--active': editPlanForm.type === 'weekly'}"
              >
                <text class="plan-type-item__text">???</text>
              </view>
              <view
                class="plan-type-item plan-type-item--disabled"
                :class="{'plan-type-item--active': editPlanForm.type === 'monthly'}"
              >
                <text class="plan-type-item__text">???</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label required">??</text>
            <picker mode="date" :value="editPlanForm.date" @change="onEditPlanDateChange">
              <view class="picker-item">{{editPlanForm.date || '?????'}}</view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">???</text>
            <view class="priority-selector">
              <view
                class="priority-item priority-item--low"
                :class="{'priority-item--active': editPlanForm.priority === 'low'}"
                @click="editPlanForm.priority = 'low'"
              >
                <text class="priority-item__text">?</text>
              </view>
              <view
                class="priority-item priority-item--medium"
                :class="{'priority-item--active': editPlanForm.priority === 'medium'}"
                @click="editPlanForm.priority = 'medium'"
              >
                <text class="priority-item__text">?</text>
              </view>
              <view
                class="priority-item priority-item--high"
                :class="{'priority-item--active': editPlanForm.priority === 'high'}"
                @click="editPlanForm.priority = 'high'"
              >
                <text class="priority-item__text">?</text>
              </view>
            </view>
          </view>



          <view class="form-item">
            <text class="form-label">??</text>
            <textarea class="form-textarea" v-model="editPlanForm.description" placeholder="??????????"></textarea>
          </view>
        </view>

        <view class="plan-modal__footer">
          <view class="btn-cancel" hover-class="btn-hover" @click="closeEditPlanModal">??</view>
          <view class="btn-primary" hover-class="btn-hover" @click="updatePlanForm">??</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import CustomTabBar from '../../components/common/CustomTabBar.uvue';
import UniPopup from '../../uni_modules/uni-popup/components/uni-popup/uni-popup.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import planApi from '../../utils/api/plan.js';

export default {
  name: 'PlanIndex',
  components: {
    CustomTabBar,
    UniPopup,
    SlaNavbar
  },
  onLoad() {
    console.log('Plan page loaded');

    // 加载计划列表
    this.loadPlans();

    // 加载统计数据
    this.loadPlanStats();
  },
  onReady() {
    // 页面渲染完成后执行
    console.log('Plan page ready');
    // 检查弹窗组件是否就绪
    if (this.$refs.addPlanPopup && this.$refs.aiPlanPopup && this.$refs.planDetailPopup) {
      console.log('All popups are ready');
    } else {
      console.log('Some popups are not ready');
    }
  },
  onShow() {
    console.log('Plan page shown');

    try {
      // 检查是否是从其他页面返回的
      // 如果是，则需要重新加载数据，而不是在onLoad中加载
      const pages = getCurrentPages();
      if (pages.length > 1) {
        // 从其他页面返回，重新加载数据
        console.log('从其他页面返回，重新加载数据');
        this.loadPlans();
        this.loadPlanStats();
      } else {
        console.log('首次进入页面，不重新加载');
      }

      // 强制更新组件以确保数据同步
      if (this && typeof this.$forceUpdate === 'function' && this.$) {
        setTimeout(() => {
          try {
            this.$forceUpdate();
          } catch (e) {
            console.warn('$forceUpdate调用失败:', e);
          }
        }, 0);
      }

      if (this && this.plans) {
        console.log('总计划数:', this.plans.length);
        console.log('今日计划:', this.todayPlans.length);
        console.log('即将到来', this.upcomingPlans.length);
        console.log('已完成', this.completedPlans.length);
        console.log('今日完成', this.todayCompletedPlansCount);
        console.log('完成率', this.todayCompletionRate + '%');

        // 调试计划分类逻辑
        this.debugPlanCategories();
      }
    } catch (error) {
      console.error('执行onShow方法出错', error);
    }
  },
  data() {
    return {
      currentDate: new Date(),
      showAddMenu: false,
      activeTab: 'today', // 当前活跃标签页
      planStats: {
        totalPlans: 0,
        completedPlans: 0,
        inProgressPlans: 0,
        checkInCount: 0
      },
      // 今日完成的计划缓存
      todayCompletedPlans: {},
      plans: [],

      // 计划表单数据
      planForm: {
        title: '',
        type: 'daily',
        date: new Date().toISOString().split('T')[0],
        priority: 'medium',
        description: '',
        completed: false
      },

      // AI计划表单
      aiPlanForm: {
        description: '',
        startDate: '',
        endDate: '',
        learningStyle: 'visual',
        skillLevel: 3,
        studyTimePerDay: '1-2小时',
        otherRequirements: ''
      },
      studyTimeOptions: ['少于1小时', '1-2小时', '2-3小时', '3-4小时', '4小时以上'],
      studyTimeIndex: 1,
      isGeneratingPlan: false,
      selectedPlan: null,

      // 编辑表单
      editPlanForm: {
        id: 0,
        title: '',
        type: 'daily',
        date: '',
        priority: 'medium',
        description: '',
        completed: false
      },
    }
  },
  computed: {
    // 今日计划
    todayPlans() {
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      console.log('今日日期:', todayStr);
      console.log('所有计划:', JSON.stringify(this.plans));

      const result = this.plans.filter(plan => {
        // 获取 date 作为开始日期
        const planStartDate = plan.date;
        const planEndDate = plan.endDate || planStartDate;

        // 检查日期是否有效
        if (!planStartDate) {
          console.log(`计划 ${plan.id} 没有开始日期`);
          return false;
        }

        // 转换为日期对象
        const startDate = new Date(planStartDate);
        const endDate = new Date(planEndDate);

        // 设置时间边界
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        today.setHours(12, 0, 0, 0); // 设置为中午时间

        console.log(`计划 ${plan.id} 类型: ${plan.type}, 开始日期: ${planStartDate}, 结束日期: ${planEndDate}`);

        // 根据计划类型判断是否为今日计划
        switch (plan.type) {
          case 'daily': // 日计划
            // 日计划只在指定日期显示
            return planStartDate === todayStr;

          case 'weekly': // 周计划
            // 周计划在整个周期内都显示
            return today >= startDate && today <= endDate;

          case 'monthly': // 月计划
            // 月计划在整个月期内都显示
            return today >= startDate && today <= endDate;

          case 'custom': // 自定义计划
          default:
            // 自定义计划在整个周期内都显示
            return today >= startDate && today <= endDate;
        }
      });

      console.log('Computed todayPlans:', result.length);
      return result;
    },

    // 即将到来
    upcomingPlans() {
      const result = this.plans.filter(plan => this.isPlanUpcoming(plan));
      console.log('Computed upcomingPlans:', result.length);
      return result;
    },

    // 已完成
    completedPlans() {
      const result = this.plans.filter(plan => this.isPlanCompleted(plan));
      console.log('Computed completedPlans:', result.length);
      return result;
    },

    // 今日完成计划数
    todayCompletedPlansCount() {
      return this.todayPlans.filter(plan => plan.todayCompleted).length;
    },

    // 完成率
    todayCompletionRate() {
      if (this.todayPlans.length === 0) return 0;
      return Math.round((this.todayCompletedPlansCount / this.todayPlans.length) * 100);
    }
  },
  methods: {
    // 判断计划是否进行中
    isPlanInProgress(plan) {
      if (!plan) {
        console.log('Invalid plan for isPlanInProgress:', plan);
        return false;
      }

      // 已完成的计划不算进行中
      if (plan.completed) {
        return false;
      }

      try {
        const now = new Date();
        const today = now.toISOString().split('T')[0];

        // 获取 date 作为开始日期
        const planStartDate = plan.date;
        const planEndDate = plan.endDate || planStartDate;

        // 检查日期格式
        if (!planStartDate || typeof planStartDate !== 'string') {
          console.error('Invalid plan date format:', planStartDate);
          return false;
        }

        // 转换为日期对象
        const startDate = new Date(planStartDate);
        const endDate = new Date(planEndDate);

        // 验证日期有效性
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          console.error('Invalid date conversion:', planStartDate, planEndDate);
          return false;
        }

        // 设置时间边界
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        now.setHours(12, 0, 0, 0); // 设置为中午时间

        console.log(`Plan ${plan.id} (${plan.title}) 类型: ${plan.type}, 开始日期: ${planStartDate}, 结束日期: ${planEndDate}`);

        // 根据计划类型判断是否进行中
        switch (plan.type) {
          case 'daily': // 日计划
            // 日计划只在指定日期显示为进行中
            const isInProgress = planStartDate === today;
            console.log(`Plan ${plan.id} (${plan.title}) in progress check: date=${planStartDate}, today=${today}, result=${isInProgress}`);
            return isInProgress;

          case 'weekly': // 周计划
          case 'monthly': // 月计划
          case 'custom': // 自定义计划
          default:
            // 其他类型计划在整个周期内都显示为进行中
            const isInRange = now >= startDate && now <= endDate;
            console.log(`Plan ${plan.id} (${plan.title}) in progress check: startDate=${planStartDate}, endDate=${planEndDate}, today=${today}, result=${isInRange}`);
            return isInRange;
        }
      } catch (error) {
        console.error('Error in isPlanInProgress:', error);
        return false;
      }
    },

    // 判断计划是否即将到来
    isPlanUpcoming(plan) {
      if (!plan) {
        console.log('Invalid plan for isPlanUpcoming:', plan);
        return false;
      }

      try {
        const now = new Date();
        const today = now.toISOString().split('T')[0];

        // 获取 date 作为开始日期
        const planStartDate = plan.date;

        // 检查日期格式
        if (!planStartDate || typeof planStartDate !== 'string') {
          console.error('Invalid plan date format:', planStartDate);
          return false;
        }

        // 转换为Date对象
        const startDate = new Date(planStartDate);
        const todayDate = new Date(today);

        // 验证日期有效性
        if (isNaN(startDate.getTime()) || isNaN(todayDate.getTime())) {
          console.error('Invalid date conversion:', planStartDate, today);
          return false;
        }

        // 设置时间边界
        startDate.setHours(0, 0, 0, 0);
        todayDate.setHours(0, 0, 0, 0);

        console.log(`Plan ${plan.id} (${plan.title}) 类型: ${plan.type}, 开始日期: ${planStartDate}`);

        // 根据计划类型判断是否即将到来
        switch (plan.type) {
          case 'daily': // 日计划
            // 日计划开始日期在今天之后
            const isUpcoming = startDate > todayDate;
            console.log(`Plan ${plan.id} (${plan.title}) upcoming check: date=${planStartDate}, today=${today}, result=${isUpcoming}`);
            return isUpcoming;

          case 'weekly': // 周计划
          case 'monthly': // 月计划
          case 'custom': // 自定义计划
          default:
            // 其他类型计划开始日期在今天之后
            const isUpcomingRange = startDate > todayDate;
            console.log(`Plan ${plan.id} (${plan.title}) upcoming check: startDate=${planStartDate}, today=${today}, result=${isUpcomingRange}`);
            return isUpcomingRange;
        }
      } catch (error) {
        console.error('Error in isPlanUpcoming:', error);
        return false;
      }
    },

    // 判断计划是否已完成
    isPlanCompleted(plan) {
      if (!plan) {
        console.log('Invalid plan for isPlanCompleted:', plan);
        return false;
      }

      // 如果已标记为完成，直接返回true
      if (plan.completed) {
        return true;
      }

      try {
        const now = new Date();
        const today = now.toISOString().split('T')[0];

        // 获取 date 作为开始日期
        const planStartDate = plan.date;

        // 获取结束日期，默认为开始日期
        const planEndDate = plan.endDate || planStartDate;

        // 检查日期格式
        if (!planEndDate || typeof planEndDate !== 'string') {
          console.error('Invalid plan end date format:', planEndDate);
          return false;
        }

        // 转换为Date对象
        const endDate = new Date(planEndDate);
        const todayDate = new Date(today);

        // 验证日期有效性
        if (isNaN(endDate.getTime()) || isNaN(todayDate.getTime())) {
          console.error('Invalid date conversion:', planEndDate, today);
          return false;
        }

        // 设置时间边界
        endDate.setHours(0, 0, 0, 0);
        todayDate.setHours(0, 0, 0, 0);

        console.log(`Plan ${plan.id} (${plan.title}) 类型: ${plan.type}, 结束日期: ${planEndDate}`);

        // 根据计划类型判断是否已完成
        switch (plan.type) {
          case 'daily': // 日计划
            // 日计划结束日期在今天之前
            const isCompleted = endDate < todayDate;
            console.log(`Plan ${plan.id} (${plan.title}) completed check: endDate=${planEndDate}, today=${today}, result=${isCompleted}`);
            return isCompleted;

          case 'weekly': // 周计划
          case 'monthly': // 月计划
          case 'custom': // 自定义计划
          default:
            // 其他类型计划结束日期在今天之前
            const isCompletedRange = endDate < todayDate;
            console.log(`Plan ${plan.id} (${plan.title}) completed check: endDate=${planEndDate}, today=${today}, result=${isCompletedRange}`);
            return isCompletedRange;
        }
      } catch (error) {
        console.error('Error in isPlanCompleted:', error);
        return false;
      }
    },

    // 获取计划类型文本
    getPlanTypeText(type) {
      const typeMap = {
        'daily': '日计划',
        'weekly': '周计划',
        'monthly': '月计划',
        'custom': '自定义计划',
        'ai': 'AI计划'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取优先级文本
    getPriorityText(priority) {
      if (priority === 'low') return '低';
      if (priority === 'medium') return '中';
      if (priority === 'high') return '高';
      return '';
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';

      try {
        const date = new Date(dateStr);

        // 验证日期有效性
        if (isNaN(date.getTime())) {
          console.error('Invalid date in formatDate:', dateStr);
          return dateStr; // 返回原始字符串
        }

        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        return `${year}年${month}月${day}日`;
      } catch (error) {
        console.error('Error in formatDate:', error, dateStr);
        return dateStr; // 返回原始字符串
      }
    },

    // 获取时间范围
    getTimeRange(subTasks) {
      if (!subTasks || !Array.isArray(subTasks) || subTasks.length === 0) {
        return '';
      }

      let startTimes = [];
      let endTimes = [];

      // 收集所有时间信息
      subTasks.forEach(task => {
        if (task.startTime) {
          startTimes.push(String(task.startTime));
        }
        if (task.endTime) {
          endTimes.push(String(task.endTime));
        }
      });

      // 如果没有时间信息，返回空
      if (startTimes.length === 0 && endTimes.length === 0) {
        return '';
      }

      // 对时间进行排序
      startTimes.sort();
      endTimes.sort();

      const earliestStart = startTimes[0];
      const latestEnd = endTimes[endTimes.length - 1];

      // 构建时间范围字符串
      if (earliestStart && latestEnd) {
        return `${earliestStart} - ${latestEnd}`;
      } else if (earliestStart) {
        return `${earliestStart} 开始`;
      } else if (latestEnd) {
        return `${latestEnd} 结束`;
      }

      return '';
    },

    // 切换添加菜单显示
    toggleAddMenu() {
      this.showAddMenu = !this.showAddMenu;
    },

    // 处理手动添加计划
    handleAddPlan() {
      this.showAddMenu = false;
      this.showAddPlanModal();
    },

    // 处理AI生成计划
    handleAiPlan() {
      this.showAddMenu = false;
      this.showAiPlanModal();
    },

    // 切换Tab
    switchTab(tabName) {
      if (this.activeTab === tabName) {
        console.log('已经是当前标签页:', tabName);
        return;
      }

      this.activeTab = tabName;
      console.log('Switched to tab:', tabName);

      // 切换标签页时重新加载数据
      this.loadPlans();
    },

    // 加载计划
    loadPlans() {
      try {
        // 确保隐藏任何加载状态
        try {
          uni.hideLoading();
        } catch (e) {
          console.error('隐藏加载失败', e);
        }

        uni.showLoading({
          title: '加载中...'
        });

        // 根据当前标签页确定状态参数
        let status = 'today';
        if (this.activeTab === 'upcoming') {
          status = 'upcoming';
        } else if (this.activeTab === 'completed') {
          status = 'completed';
        }

        // 调用API获取计划列表
        planApi.getPlanList({
          pageNum: 1,
          pageSize: 50,
          status: status
        }).then(res => {
          console.log('API响应数据:', JSON.stringify(res));

          if (res.code === 200 && res.data) {
            // 打印响应数据结构
            console.log('响应数据键名:', Object.keys(res.data));

            // 处理数据结构 (records数组)
            if (res.data.records && Array.isArray(res.data.records)) {
              console.log('使用records数组格式');
              // 转换数据格式
              this.plans = res.data.records.map(item => {
                return {
                  id: item.planId || item.id,
                  planId: item.planId || item.id, // 保持兼容性，使用planId
                  title: item.title,
                  type: item.type,
                  // 使用 date 或startDate 作为开始日期
                  date: item.date || item.startDate,
                  endDate: item.endDate,
                  priority: item.priority,
                  description: item.description,
                  completed: item.completed,
                  todayCompleted: item.todayCompleted,
                  aiGenerated: item.aiGenerated,
                  createTime: item.createTime,
                  completionTime: item.completionTime, // 完成时间
                  subTasks: item.subTasks ? item.subTasks.map(subTask => {
                    return {
                      id: subTask.subTaskId, // 子任务ID，使用subTaskId
                      subTaskId: subTask.subTaskId, // 保持兼容性，使用subTaskId
                      title: subTask.title,
                      startTime: subTask.startTime,
                      endTime: subTask.endTime,
                      completed: subTask.completed,
                      description: subTask.description,
                      completionTime: subTask.completionTime // 子任务完成时间
                    };
                  }) : [],
                  recommendedResources: item.recommendedResources || []
                };
              });
              console.log('计划数据加载:', this.plans.length);
            }
            // ??list??
            else if (res.data.list && Array.isArray(res.data.list)) {
              console.log('??list??????');
              // ????????????              this.plans = res.data.list.map(item => {
                return {
                  id: item.planId || item.id,
                  planId: item.planId || item.id, // ???????planId
                  title: item.title,
                  type: item.type,
                  // ?? date ??startDate ??????                  date: item.date || item.startDate,
                  endDate: item.endDate,
                  priority: item.priority,
                  description: item.description,
                  completed: item.completed,
                  todayCompleted: item.todayCompleted,
                  aiGenerated: item.aiGenerated,
                  createTime: item.createTime,
                  completionTime: item.completionTime, // ??????
                  subTasks: item.subTasks ? item.subTasks.map(subTask => {
                    return {
                      id: subTask.subTaskId, // ??ID?????subTaskId
                      subTaskId: subTask.subTaskId, // ???????subTaskId
                      title: subTask.title,
                      startTime: subTask.startTime,
                      endTime: subTask.endTime,
                      completed: subTask.completed,
                      description: subTask.description,
                      completionTime: subTask.completionTime // ??????????                    };
                  }) : [],
                  recommendedResources: item.recommendedResources || []
                };
              });
              console.log('????????:', this.plans.length);
            }
            // ????????????
            else if (Array.isArray(res.data)) {
              console.log('????????');
              // ????????????              this.plans = res.data.map(item => {
                return {
                  id: item.planId || item.id,
                  planId: item.planId || item.id, // ???????planId
                  title: item.title,
                  type: item.type,
                  // ?? date ??startDate ??????                  date: item.date || item.startDate,
                  endDate: item.endDate,
                  priority: item.priority,
                  description: item.description,
                  completed: item.completed,
                  todayCompleted: item.todayCompleted,
                  aiGenerated: item.aiGenerated,
                  createTime: item.createTime,
                  completionTime: item.completionTime, // ??????
                  subTasks: item.subTasks ? item.subTasks.map(subTask => {
                    return {
                      id: subTask.subTaskId, // ??ID?????subTaskId
                      subTaskId: subTask.subTaskId, // ???????subTaskId
                      title: subTask.title,
                      startTime: subTask.startTime,
                      endTime: subTask.endTime,
                      completed: subTask.completed,
                      description: subTask.description,
                      completionTime: subTask.completionTime // ??????????                    };
                  }) : [],
                  recommendedResources: item.recommendedResources || []
                };
              });
              console.log('????????:', this.plans.length);
            } else {
              // 如果list和records都不存在，设置为空数组
              console.warn('未知的数据格式');
              this.plans = [];
            }
          } else {
            console.error('获取计划失败:', res.message);
            uni.showToast({
              title: '获取计划失败',
              icon: 'none'
            });
            // 设置plans为空数组
            this.plans = [];
          }
        }).catch(err => {
          console.error('网络请求失败:', err);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
          // 设置plans为空数组
          this.plans = [];
        }).finally(() => {
          uni.hideLoading();
        });
      } catch (error) {
        console.error('loadPlans 方法出错:', error);
        // 确保隐藏加载
        uni.hideLoading();
        uni.showToast({
          title: '加载计划失败',
          icon: 'none'
        });
        // 设置plans为空数组
        this.plans = [];
      }
    },

    // 加载计划统计
    loadPlanStats() {
      planApi.getPlanStats('day').then(res => {
        if (res.code === 200 && res.data) {
          this.planStats = {
            totalPlans: res.data.totalPlans,
            completedPlans: res.data.completedPlans,
            inProgressPlans: res.data.inProgressPlans,
            checkInCount: res.data.checkInCount || 0
          };
          console.log('计划统计数据加载:', this.planStats);
        } else {
          console.error('获取统计数据失败:', res.message);
        }
      }).catch(err => {
        console.error('统计数据请求失败:', err);
      });
    },

    // 切换计划完成状态（今日打卡）
    togglePlanStatus(plan) {
      try {
        const planIndex = this.plans.findIndex(p => p.id === plan.id);

        if (planIndex !== -1) {
          // 获取当前状态
          const currentStatus = this.plans[planIndex].todayCompleted;

          // 如果已经完成，不允许重复打卡
          if (currentStatus) {
            uni.showToast({
              title: '今日已完成打卡',
              icon: 'none',
              duration: 2000
            });
            return;
          }

          // 设置新状态为完成
          const newStatus = true;

          // 构建计划信息
          const planInfo = `计划名称：${plan.title}\n优先级：${this.getPriorityText(plan.priority)}\n开始日期：${this.formatDate(plan.date)}${plan.endDate && plan.endDate !== plan.date ? '\n结束日期：' + this.formatDate(plan.endDate) : ''}\n计划类型：${this.getPlanTypeText(plan.type)}`;

          // 确认对话框
          uni.showModal({
            title: '确认打卡',
            content: `${planInfo}\n\n确定要标记今日计划为已完成吗？`,
            confirmText: '确认打卡',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                this.updatePlanStatus(plan, newStatus);
              }
            }
          });
        }
      } catch (error) {
        console.error('Error toggling plan status:', error);
      }
    },

    // 更新计划完成状态
    updatePlanStatus(plan, newStatus) {
      // 参数验证
      if (!plan || !plan.id) {
        console.error('计划参数无效');
        return;
      }

      const planIndex = this.plans.findIndex(p => (p.planId || p.id) === (plan.planId || plan.id));
      if (planIndex === -1) {
        console.error('计划不存在');
        return;
      }

      // 显示加载
      uni.showLoading({
        title: '打卡中...'
      });

      // 准备数据
      const planData = {
        planId: plan.planId || plan.id,
        title: plan.title,
        type: plan.type,
        date: plan.date,
        startDate: plan.date || plan.startDate,
        endDate: plan.endDate || plan.date,
        priority: plan.priority || 'medium',
        description: plan.description || '',
        aiGenerated: plan.aiGenerated || false,
        completionTime: new Date().toISOString()
      };

      console.log('更新数据:', JSON.stringify(planData));

      // 调用API更新状态
      planApi.updatePlanStatus(plan.planId || plan.id, planData).then(res => {
        if (res.code === 200) {
          // 更新本地状态
          this.plans[planIndex].todayCompleted = true;

          // 如果plans对象没有lastCheckInTime属性，添加它
          if (!Object.prototype.hasOwnProperty.call(this.plans[planIndex], 'lastCheckInTime')) {
            // 使用Vue的响应式方法
            this.$set(this.plans[planIndex], 'lastCheckInTime', planData.completionTime);
          } else {
            // 直接更新属性
            this.plans[planIndex].lastCheckInTime = planData.completionTime;
          }

          // 成功提示
          uni.showToast({
            title: '打卡成功',
            icon: 'success',
            duration: 1500
          });

          // 刷新统计数据
          this.loadPlanStats();
        } else {
          console.error('打卡失败:', res.message);
          uni.showToast({
            title: '打卡失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('网络请求失败:', err);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }).finally(() => {
        uni.hideLoading();
      });
    },

    // 检查并自动标记过期计划为已完成
    checkExpiredPlans() {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      this.plans.forEach((plan, index) => {
        if (!plan.completed) {
          try {
            // 获取 date 作为开始日期
            const planStartDate = plan.date;
            const planEndDate = plan.endDate || planStartDate;

            // 检查日期是否有效
            if (!planEndDate) return;

            // 转换为日期对象
            const endDate = new Date(planEndDate);

            // 验证日期有效性
            if (isNaN(endDate.getTime())) {
              console.error('Invalid date conversion:', planEndDate);
              return;
            }

            // 设置时间边界
            endDate.setHours(0, 0, 0, 0);

            // 判断是否已过期
            let isExpired = false;

            switch (plan.type) {
              case 'daily': // 日计划
                // 日计划结束日期在今天之前
                isExpired = endDate < today;
                break;

              case 'weekly': // 周计划
              case 'monthly': // 月计划
              case 'custom': // 自定义计划
              default:
                // 其他类型计划结束日期在今天之前
                isExpired = endDate < today;
                break;
            }

            // 如果过期，自动标记为完成
            if (isExpired) {
              this.plans[index].completed = true;
              this.plans[index].completionTime = new Date().toISOString();

              console.log(`Plan ${plan.id} (${plan.title}) automatically marked as completed due to expiration`);
            }
          } catch (error) {
            console.error('Error in checkExpiredPlans for plan:', plan.id, error);
          }
        }
      });

      // 更新统计数据
      this.updatePlanStats();
    },

    // 保存已完成计划到本地存储
    saveCompletedPlanToStorage(plan) {
      try {
        // 获取已存储的完成计划
        let completedPlans = uni.getStorageSync('completedPlans') || [];

        // 检查是否已存在相同ID的计划
        const existingIndex = completedPlans.findIndex(p => p.id === plan.id);
        if (existingIndex !== -1) {
          // 更新现有计划
          completedPlans[existingIndex] = plan;
        } else {
          // 添加新计划
          completedPlans.push(plan);
        }

        // 保存到本地存储
        uni.setStorageSync('completedPlans', completedPlans);
        console.log('Saved completed plan to storage:', plan);
      } catch (e) {
        console.error('Failed to save completed plan to storage:', e);
      }
    },

    // 从本地存储移除已完成计划
    removeCompletedPlanFromStorage(planId) {
      try {
        // 获取已存储的完成计划
        let completedPlans = uni.getStorageSync('completedPlans') || [];

        // 根据ID过滤掉指定计划
        completedPlans = completedPlans.filter(p => p.id !== planId);

        // 保存到本地存储
        uni.setStorageSync('completedPlans', completedPlans);
        console.log('Removed completed plan from storage, ID:', planId);
      } catch (e) {
        console.error('Failed to remove completed plan from storage:', e);
      }
    },



    // 加载今日完成状态
    loadTodayCompletedStatus() {
      try {
        const today = new Date().toISOString().split('T')[0];

        // 遍历所有计划，检查今日完成状态
        this.plans.forEach((plan, index) => {
          const key = `plan_${plan.id}_${today}`;
          const isCompleted = uni.getStorageSync(key);

          if (isCompleted) {
            this.plans[index].todayCompleted = true;
          } else {
            this.plans[index].todayCompleted = false;
          }
        });

        console.log('Loaded today\'s completion status for plans');
      } catch (e) {
        console.error('Failed to load today\'s completion status:', e);
      }
    },

    // 更新计划统计
    updatePlanStats() {
      // 检查this和this.plans是否存在
      if (!this || !this.plans) {
        console.warn('updatePlanStats: this或this.plans不存在');
        return;
      }

      // 保留原有的checkInCount
      const checkInCount = this.planStats?.checkInCount || 0;

      this.planStats = {
        totalPlans: this.plans.length,
        completedPlans: this.completedPlans.length,
        inProgressPlans: this.todayPlans.length,
        checkInCount: checkInCount
      };

      console.log('Plan stats updated:', this.planStats);
    },

    // 上一个日期
    previousDate() {
      const newDate = new Date(this.currentDate);
      if (this.activeTab === 'daily') {
        newDate.setDate(newDate.getDate() - 1);
      } else if (this.activeTab === 'weekly') {
        newDate.setDate(newDate.getDate() - 7);
      } else {
        newDate.setMonth(newDate.getMonth() - 1);
      }
      this.currentDate = newDate;
    },

    // 下一个日期
    nextDate() {
      const newDate = new Date(this.currentDate);
      if (this.activeTab === 'daily') {
        newDate.setDate(newDate.getDate() + 1);
      } else if (this.activeTab === 'weekly') {
        newDate.setDate(newDate.getDate() + 7);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      this.currentDate = newDate;
    },

    // 获取周数
    getWeekNumber() {
      const d = new Date(Date.UTC(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate()));
      const dayNum = d.getUTCDay() || 7;
      d.setUTCDate(d.getUTCDate() + 4 - dayNum);
      const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
      // 计算周数
      return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
    },

    // 切换标签页 - 与switchTab方法功能相同
    changeTab(tabName) {
      if (this.activeTab === tabName) {
        console.log('已经是当前标签页:', tabName);
        return;
      }

      this.activeTab = tabName;
      console.log('Switched to tab:', tabName);

      // 切换标签页时重新加载数据
      this.loadPlans();

      // 强制更新
      if (this && typeof this.$forceUpdate === 'function') {
        this.$forceUpdate();
      } else {
        console.warn('changeTab: $forceUpdate方法不存在');
      }
    },

    // 显示添加计划模态框
    showAddPlanModal() {
      console.log('Showing add plan modal');
      // 重置表单
      this.resetPlanForm();

      // 导航到添加计划页面，如果失败则显示模态框
      uni.navigateTo({
        url: '/pages/plan/add',
        fail: (err) => {
          console.error('Failed to navigate to add plan page:', err);
          // 如果导航失败，显示模态框
          if (this.$refs.addPlanPopup) {
            this.$refs.addPlanPopup.open();
          } else {
            console.error('Add plan popup not initialized');
            uni.showToast({
              title: '无法打开添加页面',
              icon: 'none'
            });
          }
        }
      });
    },

    // 关闭添加计划模态框
    closeAddPlanModal() {
      if (this.$refs.addPlanPopup) {
        this.$refs.addPlanPopup.close();
      }
    },

    // 重置表单
    resetPlanForm() {
      this.planForm = {
        title: '',
        type: this.activeTab, // 根据当前标签页设置计划类型
        date: new Date().toISOString().split('T')[0],
        priority: 'medium',
        description: '',
        completed: false
      };
    },

    // 计划日期变更处理
    onPlanDateChange(e) {
      this.planForm.date = e.detail.value;
    },

    // 保存计划表单
    savePlanForm() {
      try {
        // 表单验证
        if (!this.planForm.title) {
          uni.showToast({
            title: '请输入计划标题',
            icon: 'none'
          });
          return;
        }

        if (!this.planForm.date) {
          uni.showToast({
            title: '请选择日期',
            icon: 'none'
          });
          return;
        }

        // 确保先隐藏之前的加载状态
        try {
          uni.hideLoading();
        } catch (e) {
          console.error('隐藏加载状态失败', e);
        }

        // 显示加载
        uni.showLoading({
          title: '创建中...'
        });

        // 准备数据
        const planData = {
          title: this.planForm.title,
          type: this.planForm.type,
          date: this.planForm.date,
          endDate: this.planForm.date, // 默认结束日期等于开始日期
          priority: this.planForm.priority,
          description: this.planForm.description,
          customDays: this.planForm.type === 'custom' ? (this.planForm.customDays || 1) : 1 // 自定义天数
        };

        // 调用API创建计划
        planApi.createPlan(planData).then(res => {
          if (res.code === 200) {
            // 关闭模态框
            this.closeAddPlanModal();

            // 切换到对应的标签页
            this.changeTab(this.planForm.type);

            // 重新加载计划列表
            this.loadPlans();

            // 成功提示
            uni.showToast({
              title: '创建成功',
              icon: 'success'
            });
          } else {
            console.error('创建失败:', res.message);
            uni.showToast({
              title: res.message || '创建失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          console.error('网络错误:', err);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }).finally(() => {
          uni.hideLoading();
        });
      } catch (error) {
        console.error('savePlanForm 方法出错:', error);
        // 确保隐藏加载
        uni.hideLoading();
        uni.showToast({
          title: '创建计划失败',
          icon: 'none'
        });
      }
    },

    // 导航到AI页面
    navigateToAI(showToolbox = false) {
      uni.switchTab({
        url: '/pages/ai/index'
      });

      // 设置是否显示AI工具箱的标识
      uni.setStorageSync('showAiToolbox', showToolbox);
    },

    // 处理标签页点击
    handleTabClick(index) {
      // 这个方法由CustomTabBar组件调用，暂时只记录日志
      console.log('Tab clicked:', index);
    },

    // AI计划模态框
    showAiPlanModal() {
      console.log('Showing AI plan modal');

      // 导航到AI计划页面
      uni.navigateTo({
        url: '/pages/plan/ai-create',
        fail: (err) => {
          console.error('Failed to navigate to AI create plan page:', err);
          // 如果导航失败，显示模态框
          if (this.$refs.aiPlanPopup) {
            // 重置AI表单
            this.resetAiPlanForm();
            this.$refs.aiPlanPopup.open();
          } else {
            console.error('AI plan popup not initialized');
            uni.showToast({
              title: '无法打开AI计划页面',
              icon: 'none'
            });
          }
        }
      });
    },

    closeAiPlanModal() {
      if (this.$refs.aiPlanPopup) {
        this.$refs.aiPlanPopup.close();
      }
    },

    onStartDateChange(e) {
      this.aiPlanForm.startDate = e.detail.value;

      // 根据计划类型自动计算结束日期
      const startDate = new Date(this.aiPlanForm.startDate);
      let endDate = new Date(startDate);

      if (this.aiPlanForm.planType === 'daily') {
        // 日计划结束日期等于开始日期
        this.aiPlanForm.endDate = this.aiPlanForm.startDate;
      } else if (this.aiPlanForm.planType === 'weekly') {
        // 周计划结束日期为开始日期加6天（一周）
        endDate.setDate(startDate.getDate() + 6);
        this.aiPlanForm.endDate = this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'monthly') {
        // 月计划结束日期为当月最后一天
        endDate.setMonth(startDate.getMonth() + 1);
        endDate.setDate(0); // 设置为上月最后一天
        this.aiPlanForm.endDate = this.formatDate(endDate);
      } else if (this.aiPlanForm.planType === 'custom') {
        // 自定义计划根据自定义天数计算结束日期
        endDate.setDate(startDate.getDate() + this.aiPlanForm.customDays - 1);
        this.aiPlanForm.endDate = this.formatDate(endDate);
      }
    },

    selectLearningStyle(style) {
      this.aiPlanForm.learningStyle = style;
    },

    onSkillLevelChange(e) {
      this.aiPlanForm.skillLevel = e.detail.value;
    },

    onStudyTimeChange(e) {
      this.studyTimeIndex = e.detail.value;
      this.aiPlanForm.studyTimePerDay = this.studyTimeOptions[this.studyTimeIndex];
    },

    generateAiPlan() {
      try {
        // 表单验证
        if (!this.aiPlanForm.description) {
          uni.showToast({
            title: '请输入学习内容',
            icon: 'none'
          });
          return;
        }

        if (!this.aiPlanForm.startDate) {
          uni.showToast({
            title: '请选择开始日期',
            icon: 'none'
          });
          return;
        }

        // 设置生成状态
        this.isGeneratingPlan = true;

        // 确保有结束日期
        if (!this.aiPlanForm.endDate) {
          this.aiPlanForm.endDate = this.aiPlanForm.startDate;
        }

        // 构建请求数据，按照后端API的格式
        const aiPlanData = {
          description: this.aiPlanForm.description,
          start_date: this.aiPlanForm.startDate,
          end_date: this.aiPlanForm.endDate,
          learning_style: this.aiPlanForm.learningStyle || 'visual', // visual-视觉型，auditory-听觉型，reading-阅读型，kinesthetic-动觉型
          skill_level: this.aiPlanForm.skillLevel || 3, // 1-5级别，1最低，5最高
          study_time_per_day: this.aiPlanForm.studyTimePerDay || '1-2小时',
          other_requirements: this.aiPlanForm.otherRequirements || '',
          plan_type: this.aiPlanForm.planType || 'daily', // daily-日计划，weekly-周计划，monthly-月计划，custom-自定义
          custom_days: this.aiPlanForm.planType === 'custom' ? (this.aiPlanForm.customDays || 1) : 1,
          subtask_count: 3 // 默认生成3个子任务
        };

        // 显示生成提示
        uni.showToast({
          title: 'AI计划生成中',
          icon: 'loading',
          duration: 2000
        });

        // 提示用户AI生成需要一些时间
        setTimeout(() => {
          uni.showToast({
            title: '预计需要3-5分钟',
            icon: 'none',
            duration: 3000
          });
        }, 2500);

        // 重置表单
        this.resetAiPlanForm();

        // 关闭模态框
        this.closeAiPlanModal();

        // 切换到今日计划
        this.changeTab('today');

        // 调用API生成AI计划
        console.log('发送AI计划生成请求:', JSON.stringify(aiPlanData));
        planApi.generateAiPlan(aiPlanData).then(res => {
          console.log('AI计划生成响应:', JSON.stringify(res));
          if (res.code === 200 && res.data) {
            // 获取计划ID
            const planId = res.data.planId;
            console.log('AI生成的计划ID:', planId);

            // 成功提示
            uni.showToast({
              title: 'AI计划生成成功',
              icon: 'success',
              duration: 2000
            });

            // 刷新计划列表
            console.log('刷新计划列表');
            this.loadPlans();

            // 跳转到计划详情
            setTimeout(() => {
              console.log('跳转到计划详情，ID:', planId);
              uni.navigateTo({
                url: `/pages/plan/detail?id=${planId}`,
                success: () => {
                  console.log('跳转成功');
                },
                fail: (err) => {
                  console.error('跳转失败:', err);
                }
              });
            }, 1000);
          } else {
            console.error('生成AI计划失败:', res.message);
            uni.showToast({
              title: res.message || '生成失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          console.error('生成AI计划出错:', err);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }).finally(() => {
          this.isGeneratingPlan = false;
          console.log('AI计划生成完成');
        });
      } catch (error) {
        console.error('generateAiPlan 方法出错:', error);
        // 重置生成状态
        this.isGeneratingPlan = false;
        uni.showToast({
          title: '生成计划失败',
          icon: 'none'
        });
      }
    },

    // 根据表单生成AI子任务
    generateSubtasks(form) {
      const subtasks = [];

      // 根据学习内容生成不同的子任务
      if (form.description.includes('英语')) {
        subtasks.push(
          {
            id: 1,
            title: '单词学习',
            startTime: '09:00',
            endTime: '10:00',
            completed: false,
            description: '学习新单词并进行记忆练习'
          },
          {
            id: 2,
            title: '语法练习',
            startTime: '14:00',
            endTime: '15:00',
            completed: false,
            description: '完成语法练习题和语法点复习'
          },
          {
            id: 3,
            title: '口语练习',
            startTime: '19:00',
            endTime: '20:00',
            completed: false,
            description: '进行口语对话练习和发音训练'
          }
        );
      } else if (form.description.includes('数学')) {
        subtasks.push(
          {
            id: 1,
            title: '理论学习',
            startTime: '09:00',
            endTime: '10:30',
            completed: false,
            description: '学习数学理论知识'
          },
          {
            id: 2,
            title: '习题练习',
            startTime: '14:00',
            endTime: '15:30',
            completed: false,
            description: '完成相关习题和练习题'
          },
          {
            id: 3,
            title: '总结复习',
            startTime: '19:00',
            endTime: '20:30',
            completed: false,
            description: '总结学习内容并进行复习'
          }
        );
      } else {
        // 通用任务
        subtasks.push(
          {
            id: 1,
            title: '基础学习',
            startTime: '09:00',
            endTime: '10:30',
            completed: false,
            description: '学习基础知识和概念'
          },
          {
            id: 2,
            title: '实践练习',
            startTime: '14:00',
            endTime: '15:30',
            completed: false,
            description: '进行实践练习和应用训练'
          },
          {
            id: 3,
            title: '复习巩固',
            startTime: '19:00',
            endTime: '20:00',
            completed: false,
            description: '复习学习内容并进行巩固'
          }
        );
      }

      return subtasks;
    },

    // 根据表单生成AI学习资源
    generateResources(form) {
      const resources = [];

      // 根据学习内容生成不同的资源
      if (form.description.includes('英语')) {
        if (form.learningStyle === 'visual') {
          resources.push(
            {
              resourceId: 1,
              title: '英语视觉学习资源',
              type: 'video',
              url: 'https://example.com/visual-vocab',
              description: '适合视觉学习者的英语教学视频',
              difficulty: form.skillLevel,
              relevance: 5
            }
          );
        } else if (form.learningStyle === 'auditory') {
          resources.push(
            {
              resourceId: 1,
              title: '英语听力练习资源',
              type: 'audio',
              url: 'https://example.com/listening-practice',
              description: '适合听觉学习者的英语听力材料',
              difficulty: form.skillLevel,
              relevance: 5
            }
          );
        }

        resources.push(
          {
            resourceId: 2,
            title: '英语学习应用',
            type: 'tool',
            url: 'https://example.com/english-apps',
            description: '推荐的英语学习移动应用',
            difficulty: Math.min(form.skillLevel + 1, 5),
            relevance: 4
          }
        );
        resources.push(
          {
            resourceId: 3,
            title: '英语学习指南',
            type: 'article',
            url: 'https://example.com/english-guide',
            description: '英语学习方法指南',
            difficulty: Math.max(form.skillLevel - 1, 1),
            relevance: 4
          }
        );
      } else if (form.description.includes('数学')) {
        resources.push(
          {
            resourceId: 1,
            title: '数学可视化工具',
            type: 'tool',
            url: 'https://example.com/math-visual',
            description: '数学概念可视化学习工具',
            difficulty: form.skillLevel,
            relevance: 5
          }
        );
        resources.push(
          {
            resourceId: 2,
            title: '数学解题技巧视频',
            type: 'video',
            url: 'https://example.com/math-techniques',
            description: '数学解题方法和技巧讲解',
            difficulty: form.skillLevel,
            relevance: 5
          }
        );
        resources.push(
          {
            resourceId: 3,
            title: '数学练习题库',
            type: 'tool',
            url: 'https://example.com/math-exercises',
            description: '数学练习题集合',
            difficulty: form.skillLevel,
            relevance: 4
          }
        );
      } else {
        // 通用资源
        resources.push(
          {
            resourceId: 1,
            title: '学习指南',
            type: 'article',
            url: 'https://example.com/learning-guide',
            description: '通用学习方法指南',
            difficulty: 2,
            relevance: 4
          }
        );
        resources.push(
          {
            resourceId: 2,
            title: '学习工具',
            type: 'tool',
            url: 'https://example.com/knowledge-tools',
            description: '通用知识管理和学习工具',
            difficulty: 3,
            relevance: 3
          }
        );
        resources.push(
          {
            resourceId: 3,
            title: '专注力和动机视频',
            type: 'video',
            url: 'https://example.com/focus-motivation',
            description: '提高学习专注力和动机的视频',
            difficulty: 2,
            relevance: 4
          }
        );
      }

      return resources;
    },

    // 重置AI表单
    resetAiPlanForm() {
      // 获取今日日期
      const today = new Date();

      // 格式化日期
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      this.aiPlanForm = {
        description: '',
        startDate: formatDate(today),
        endDate: formatDate(today),
        learningStyle: 'visual',
        skillLevel: 3,
        studyTimePerDay: '1-2小时',
        otherRequirements: '',
        planType: 'daily',
        customDays: 1
      };
      this.studyTimeIndex = 1;
    },

    // 查看计划详情
    viewPlanDetail(plan) {
      try {
        console.log('Viewing plan detail, plan ID:', plan.id);

        // 深拷贝计划对象以避免引用问题
        this.selectedPlan = JSON.parse(JSON.stringify(plan));

        // 导航到详情页面
        uni.navigateTo({
          url: '/pages/plan/detail?id=' + plan.id,
          fail: (err) => {
            console.error('Failed to navigate to plan detail page:', err);

            // 如果导航失败，显示模态框
            if (this.$refs.planDetailPopup && this.$refs.planDetailPopup.open) {
              this.$refs.planDetailPopup.open();
            } else {
              console.error('Plan detail popup not initialized');
              uni.showToast({
                title: '无法打开详情',
                icon: 'none'
              });
            }
          }
        });
      } catch (error) {
        console.error('Error in viewPlanDetail:', error);
        uni.showToast({
          title: '打开详情失败',
          icon: 'none'
        });
      }
    },

    // 关闭计划详情模态框
    closePlanDetailModal() {
      if (this.$refs.planDetailPopup && this.$refs.planDetailPopup.close) {
        this.$refs.planDetailPopup.close();
      }
    },

    // 切换子任务完成状态
    toggleSubtaskStatus(index) {
      if (this.selectedPlan && this.selectedPlan.subTasks && index < this.selectedPlan.subTasks.length) {
        this.selectedPlan.subTasks[index].completed = !this.selectedPlan.subTasks[index].completed;

        // 同步更新主计划列表中的数据
        const planIndex = this.plans.findIndex(p => p.id === this.selectedPlan?.id);
        if (planIndex !== -1 && this.plans[planIndex].subTasks && index < this.plans[planIndex].subTasks.length) {
          this.plans[planIndex].subTasks[index].completed = this.selectedPlan.subTasks[index].completed;
        }
      }
    },

    // 保存计划
    savePlan() {
      if (this.selectedPlan) {
        // 更新计划保存状态
        const planIndex = this.plans.findIndex(p => p.id === this.selectedPlan?.id);
        if (planIndex !== -1) {
          this.plans[planIndex].saved = true;
          if (this.selectedPlan) {
            this.selectedPlan.saved = true;
          }
        }

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
      }
    },

    // 打开资源
    openResource(resource) {
      // 确认对话框
      uni.showModal({
        title: '打开资源',
        content: `确定要打开: ${resource.title}?`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '资源已打开',
              icon: 'success'
            });
          }
        }
      });
    },

    // 获取资源类型图标
    getResourceTypeIcon(type) {
      const icons = {
        'book': '📚',
        'video': '🎥',
        'article': '📄',
        'course': '🎓',
        'tool': '🔧',
        'audio': '🎵'
      };
      return icons[type] || '📋';
    },

    // 格式化中文日期
    formatDateChinese(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();

      return `${year}年${month}月${day}日`;
    },

    // 获取优先级文本 - 这是一个重复的方法，与前面的方法功能相同
    getPriorityText(priority) {
      if (priority === 'low') return '低';
      if (priority === 'medium') return '中';
      if (priority === 'high') return '高';
      return '';
    },

    // 确保计划正确显示
    ensurePlansDisplay() {
      console.log('确保计划正确显示');

      // 检查是否有计划数据
      if (!this.plans || this.plans.length === 0) {
        console.log('没有计划数据');
        return;
      }

      // 统计计划数量
      const inProgressCount = this.inProgressPlans.length;
      const upcomingCount = this.upcomingPlans.length;

      console.log(`进行中计划 ${inProgressCount}, 即将到来 ${upcomingCount}`);

      // 如果没有显示计划
      if (inProgressCount === 0 && upcomingCount === 0) {
        console.log('没有显示的计划，开始调试');

        // 逐个检查计划状态
        this.plans.forEach((plan, index) => {
          console.log(`检查计划${index + 1}/${this.plans.length}: ${plan.title}`);
          const isInProgress = this.isPlanInProgress(plan);
          const isUpcoming = this.isPlanUpcoming(plan);
          console.log(`计划 ${plan.id} (${plan.title}) - 进行中: ${isInProgress}, 即将到来: ${isUpcoming}, 已完成: ${plan.completed}`);
        });

        // 强制更新组件
        if (this && typeof this.$forceUpdate === 'function') {
          this.$forceUpdate();
        } else {
          console.warn('ensurePlansDisplay: $forceUpdate方法不存在');
        }

        // 延迟再次检查
        setTimeout(() => {
          console.log('延迟检查结果');
          console.log(`进行中计划 ${this.inProgressPlans.length}, 即将到来 ${this.upcomingPlans.length}`);
          if (this && typeof this.$forceUpdate === 'function') {
            this.$forceUpdate();
          } else {
            console.warn('ensurePlansDisplay setTimeout: $forceUpdate方法不存在');
          }
        }, 500);
      }
    },

    // 导航到历史页面
    navigateToHistory() {
      uni.navigateTo({
        url: '/pages/plan/history',
        fail: (err) => {
          console.error('Failed to navigate to history page:', err);
          uni.showToast({
            title: '无法打开历史页面',
            icon: 'none'
          });
        }
      });
    },

    // 编辑计划相关方法 - 以下是编辑功能
    // 这些方法用于计划的编辑操作

    // 显示编辑计划模态框
    showEditPlanModal() {
      if (!this.selectedPlan) {
        uni.showToast({
          title: '请先选择计划',
          icon: 'none'
        });
        return;
      }

      // 填充编辑表单
      if (this.selectedPlan) {
        this.editPlanForm = {
          id: this.selectedPlan.id,
          title: this.selectedPlan.title,
          type: this.selectedPlan.type,
          date: this.selectedPlan.date,
          priority: this.selectedPlan.priority || 'medium',
          description: this.selectedPlan.description || '',
          completed: this.selectedPlan.completed || false
        };
      }

      // 打开模态框
      this.$refs.editPlanPopup.open();
    },

    // 关闭编辑计划模态框
    closeEditPlanModal() {
      if (this.$refs.editPlanPopup) {
        this.$refs.editPlanPopup.close();
      }
    },

    // 编辑计划日期变更
    onEditPlanDateChange(e) {
      this.editPlanForm.date = e.detail.value;
    },

    // 编辑计划开始时间变更
    onEditPlanStartTimeChange(e) {
      this.editPlanForm.startTime = e.detail.value;
    },

    // 编辑计划结束时间变更
    onEditPlanEndTimeChange(e) {
      this.editPlanForm.endTime = e.detail.value;
    },

    // ????
    updatePlanForm() {
      // ????
      if (!this.editPlanForm.title) {
        uni.showToast({
          title: '????????,
          icon: 'none'
        });
        return;
      }

      if (!this.editPlanForm.date) {
        uni.showToast({
          title: '?????',
          icon: 'none'
        });
        return;
      }

      // ????????      const planIndex = this.plans.findIndex(p => p.id === this.editPlanForm.id);
      if (planIndex !== -1) {
        // ?????????ID?createTime
        const originalType = this.plans[planIndex].type;
        const originalId = this.plans[planIndex].id;
        const originalCreateTime = this.plans[planIndex].createTime;

        // ????
        const updatedPlan = {
          ...this.editPlanForm,
          type: originalType, // ????????          id: originalId, // ???ID??
          createTime: originalCreateTime, // ????????          subTasks: this.plans[planIndex].subTasks || [] // ????????        };

        // ???plans??
        this.plans[planIndex] = updatedPlan;

        // ?????????????????????????        if (this.selectedPlan && this.editPlanForm.id !== null && this.selectedPlan.id === this.editPlanForm.id) {
          this.selectedPlan = JSON.parse(JSON.stringify(this.plans[planIndex])) as Plan;
        }

        // ????
        this.closeEditPlanModal();

        // ????
        uni.showToast({
          title: '??????',
          icon: 'success'
        });
      } else {
        uni.showToast({
          title: '??????,
          icon: 'none'
        });
      }
    },

    // ??????????????????
    debugPlanCategories() {
      console.log('======= ?????? =======');
      console.log('??????', this.plans.length);

      // ?????????
      this.plans.forEach(plan => {
        console.log(`?? ${plan.id} (${plan.title}):`);
        console.log(`  - ??: ${plan.type}`);
        console.log(`  - ????? ${plan.date}`);
        console.log(`  - ????: ${plan.endDate || plan.date}`);

        // ?????????
        const isToday = this.todayPlans.some(p => p.id === plan.id);
        console.log(`  - ???????? ${isToday}`);

        // ???????????        const isUpcoming = this.upcomingPlans.some(p => p.id === plan.id);
        console.log(`  - ????????? ${isUpcoming}`);

        // ???????????        const isCompleted = this.completedPlans.some(p => p.id === plan.id);
        console.log(`  - ????????: ${isCompleted}`);

        console.log('----------------------------');
      });

      console.log('======= ???? =======');
    }
  }
}
</script>

<style>
.plan-container {
  min-height: 800px;
  display: flex;
  flex-direction: column;
  padding-bottom: 68px; /* ??????????????*/
  position: relative;
  background-image:
    radial-gradient(circle at 90% 10%, rgba(138, 123, 255, 0.1) 0%, rgba(138, 123, 255, 0) 30%),
    radial-gradient(circle at 10% 90%, rgba(91, 127, 255, 0.08) 0%, rgba(91, 127, 255, 0) 40%),
    linear-gradient(to bottom, #F0F4FF, #F8FAFF); /* ?????? */
  background-attachment: fixed;
}

.history-button {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 999px;
  box-shadow: 0 3px 12px rgba(91, 127, 255, 0.15);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.history-button:active {
  transform: scale(0.92);
  box-shadow: 0 2px 5px rgba(91, 127, 255, 0.2);
}

.history-icon {
  font-size: 20px;
  color: #5B7FFF; /* ?????? */
}

.plan-content {
  flex: 1;
  height: 700px; /* ??????????????????????????*/
  padding: 16px;
  position: relative;
  overflow-y: auto; /* ?????? */
}

/* ?????? */
.plan-stats {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 12px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 18px;

  box-shadow: 0 8px 24px rgba(74, 111, 227, 0.08);
  overflow: hidden;
}

.stat-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 14px;
  padding: 12px 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.04);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.7);
  overflow: hidden;
}



.stat-item:nth-child(1)::after {
  background: linear-gradient(to right, #6A5AE0, #8C72FF);
}

.stat-item:nth-child(2)::after {
  background: linear-gradient(to right, #4CAF50, #8BC34A);
}

.stat-item:nth-child(3)::after {
  background: linear-gradient(to right, #FF9800, #FFEB3B);
}

.stat-item:nth-child(4)::after {
  background: linear-gradient(to right, #FF5252, #FF8A80);
}

.stat-item:nth-child(5)::after {
  background: linear-gradient(to right, #00BCD4, #80DEEA);
}

.stat-item:active {
  transform: translateY(2px) scale(0.98);
  box-shadow: 0 2px 8px rgba(31, 60, 136, 0.02);
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 4px;
  background-clip: text;


  position: relative;
}

.stat-item:nth-child(1) .stat-value {
  background-image: linear-gradient(to right, #6A5AE0, #8C72FF);
}

.stat-item:nth-child(2) .stat-value {
  background-image: linear-gradient(to right, #4CAF50, #8BC34A);
}

.stat-item:nth-child(3) .stat-value {
  background-image: linear-gradient(to right, #FF9800, #FFEB3B);
}

.stat-item:nth-child(4) .stat-value {
  background-image: linear-gradient(to right, #FF5252, #FF8A80);
}

.stat-item:nth-child(5) .stat-value {
  background-image: linear-gradient(to right, #00BCD4, #80DEEA);
}

.stat-label {
  font-size: 12px;
  color: #5E6C84;
  font-weight: bold;
  text-align: center;
}

/* ???? */
.plan-section {
  margin-bottom: 8px; /* ?????? */
  padding: 0 2px;
}

/* ??????*/
.empty-state {
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  margin-bottom: 16px;
  text-align: center;
}

/* ?????? */
.floating-button {
  position: fixed;
  right: 24px;
  bottom: 88px;
  width: 64px;
  height: 64px;
  border-radius: 32px;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 20px rgba(91, 127, 255, 0.35),
              0 2px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 2px rgba(255, 255, 255, 0.3);
  z-index: 100;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}



.floating-button:active {
  transform: scale(0.92) rotate(45deg);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.2),
              0 1px 4px rgba(0, 0, 0, 0.1);
}

.floating-button:

.floating-button__icon {
  font-size: 32px;
  color: #FFFFFF;
  font-weight: normal;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

/* ???? */
.add-menu {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 99;

}

.add-menu-backdrop {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(17, 25, 40, 0.65);

}

.add-menu-content {
  position: absolute;
  right: 24px;
  bottom: 160px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  animation: slide-up 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
}


  to { transform: translateY(0); opacity: 1; }
}

.add-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px 24px;
  margin-bottom: 16px;
  box-shadow: 0 8px 20px rgba(31, 60, 136, 0.15),
              0 2px 8px rgba(0, 0, 0, 0.05);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}



.add-menu-item:active {
  transform: scale(0.96) translateY(2px);
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.1),
              0 1px 4px rgba(0, 0, 0, 0.03);
}

.add-menu-item:

.add-menu-item:nth-child(1) {
  background: linear-gradient(to right, #FFFFFF, #F0F7FF);
}

.add-menu-item:nth-child(2) {
  background: linear-gradient(to right, #FFFFFF, #F5F0FF);
}

.add-menu-item__icon {
  font-size: 22px;
  margin-right: 12px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 999px;
  position: relative;
  z-index: 2;
}

.add-menu-item:nth-child(1) .add-menu-item__icon {
  color: #FFFFFF;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 2px 8px rgba(91, 127, 255, 0.25);
}

.add-menu-item:nth-child(2) .add-menu-item__icon {
  color: #FFFFFF;
  background: linear-gradient(135deg, #6A5AE0, #9F7FFA);
  box-shadow: 0 2px 8px rgba(106, 90, 224, 0.25);
}

.add-menu-item__text {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  position: relative;
  z-index: 2;
}

/* ??????*/
.plan-tabs {
  display: flex;
  flex-direction: row;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 14px; /* ???? */
  margin-bottom: 14px; /* ?????? */
  box-shadow: 0 3px 10px rgba(31, 60, 136, 0.05); /* ???? */
  overflow: hidden;
  padding: 3px;

}

.plan-tab {
  flex: 1;
  padding: 10px 0; /* ?????? */
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px; /* ???? */
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.plan-tab--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.plan-tab__text {
  font-size: 14px; /* ???? */
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.plan-list-container {
  flex: 1;
  min-height: 400px; /* ???????????????*/
  padding-bottom: 100px; /* ?????????????????? */
}

.plan-section {
  margin-bottom: 16px;
}

/* ???? */
.plan-list {
  background-color: transparent;
  border-radius: 16px;
  padding: 8px;
  min-height: 100px;
  max-height: none; /* ?????????*/
  display: flex;
  flex-direction: column;
  gap: 12px; /* ????????????? */
}

/* ?????? */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.05);
  overflow: hidden;
}

.history-date {
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  padding-bottom: 8px;
}

.history-date-text {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
}

.history-stats {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin-bottom: 16px;
  background-color: rgba(91, 127, 255, 0.05);
  border-radius: 12px;
  padding: 12px;
}

.history-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.history-stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #5B7FFF;
}

.history-stat-label {
  font-size: 12px;
  color: #5E6C84;
  margin-top: 4px;
}

.history-plans {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-plan-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px;
  border-radius: 10px;
  background-color: rgba(240, 243, 250, 0.5);
}

.history-plan-checkbox {
  width: 18px;
  height: 18px;
  border-radius: 999px;
  border: 2px solid #80B8F5;
  margin-right: 10px;
  flex-shrink: 0;
}

.history-plan-checkbox--checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
}



.history-plan-title {
  font-size: 14px;
  color: #2E3A59;
}

.history-plan-title--completed {
  color: #8F9BB3;
  text-decoration: line-through;
}

/* ?????? */
.plan-type {
  font-size: 11px;
  color: #5B7FFF;
  background-color: rgba(91, 127, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 8px;
  font-weight: bold;
}

/* ???? */
.plan-date {
  font-size: 11px;
  color: #5E6C84;
  margin-right: 8px;
}

/* ????*/
.plan-tabs {
  display: flex;
  flex-direction: row;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(31, 60, 136, 0.08);
  overflow: hidden;
  padding: 4px;

  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative;
  z-index: 2;
}

.plan-tab {
  flex: 1;
  padding: 12px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  transition-property: transform, opacity; transition-duration: 0.35s; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}



.plan-tab:

.plan-tab--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.25);
  transform: translateY(-1px);
}

.plan-tab__text {
  font-size: 15px;
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
  z-index: 2;
}

.plan-tab--active .plan-tab__text {
  color: #FFFFFF;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ??????*/
.date-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 8px 0;
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.06);
}

.date-arrow {
  font-size: 20px;
  color: #5B7FFF;
  padding: 8px 16px;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.date-arrow:active {
  transform: scale(0.9);
}

.date-text {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  padding: 0 20px;
}

/* ???? */
.plan-list {
  background-color: transparent;
  border-radius: 16px;
  padding: 8px;
  min-height: 100px;
  max-height: none; /* ?????????*/
}

/* ??????- ????????*/
.plan-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 14px 16px;
  margin-bottom: 0; /* ??????????????gap???? */
  border-radius: 16px;
  background-color: #FFFFFF;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  border: 1px solid rgba(240, 240, 240, 0.8);
  position: relative;
  overflow: hidden;
}



.plan-row--completed {
  background-color: #FAFAFA;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  opacity: 0.85;
}



.plan-row:last-child {
  margin-bottom: 0;
}

.plan-row:active {
  transform: translateY(2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.plan-row__checkbox {
  width: 24px;
  height: 24px;
  border-radius: 999px;
  border: 2px solid #4A6FE3;
  margin-right: 12px;
  flex-shrink: 0;
  position: relative;
  transition-property: transform, opacity; transition-duration: 0.25s; transition-timing-function: ease;
  margin-top: 2px;
}

.plan-row__checkbox--checked {
  background-color: #4A6FE3;
}

.plan-row__checkbox--disabled {
  border: 2px solid #d0d0d0;
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.plan-row__check-icon {
  color: #FFFFFF;
  font-size: 14px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.plan-row__body {
  flex: 1;
  min-width: 0; /* ??flex??????*/
  display: flex;
  flex-direction: column;
}

.plan-row__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  width: 100%;
}

.plan-row__tags {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
  margin-left: 8px;
  flex-shrink: 0;
}

.plan-row__title {
  font-size: 15px;
  color: #2E3A59;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  padding-right: 5px;
}

.plan-row__title--completed {
  color: #90A0B7;
  text-decoration: line-through;
  font-weight: 400;
}

.plan-row__info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.plan-row__date {
  font-size: 11px;
  color: #4A6FE3;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin-right: 8px;
  background-color: rgba(74, 111, 227, 0.08);
  padding: 2px 8px;
  border-radius: 12px;
}



.plan-row__time {
  font-size: 11px;
  color: #5E6C84;
  display: flex;
  align-items: center;
  background-color: rgba(94, 108, 132, 0.08);
  padding: 2px 8px;
  border-radius: 12px;
}



.plan-row__tag {
  font-size: 11px;
  color: #FFFFFF;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  padding: 2px 6px;
  border-radius: 8px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(91, 127, 255, 0.2);
}

.plan-row__tag--low {
  background: linear-gradient(135deg, #52C41A, #A0D911);
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

.plan-row__tag--medium {
  background: linear-gradient(135deg, #FAAD14, #FFC53D);
  box-shadow: 0 2px 4px rgba(250, 173, 20, 0.2);
}

.plan-row__tag--high {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

.plan-row__tag--type {
  background: linear-gradient(135deg, #722ED1, #B37FEB);
  box-shadow: 0 2px 4px rgba(114, 46, 209, 0.2);
}

.plan-row--completed .plan-row__tag {
  opacity: 0.85; /* ???????????????????????*/
}

/* ??????*/
.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(250, 250, 255, 0.85));
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}



.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.9);
  width: 80px;
  height: 80px;
  border-radius: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(31, 60, 136, 0.1),
              inset 0 -2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.empty-title {
  font-size: 18px;
  color: #4A6FE3;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #8F9BB3;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

/* ?????? */
.add-plan {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px dashed #E0E0E0;
  margin-top: 8px;
}

.add-plan__icon {
  font-size: 20px;
  color: #80B8F5;
  margin-right: 8px;
}

.add-plan__text {
  font-size: 14px;
  color: #80B8F5;
}

/* ?????? */
.plan-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  padding: 16px 0;
  border-top: 1px dashed #E0E0E0;
  margin-top: 8px;
}

/* AI???? */
.ai-plan {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #F0F8FF;
  border-radius: 20px;
  border: 1px solid #80B8F5;
}

.ai-plan__icon {
  font-size: 18px;
  margin-right: 8px;
}

.ai-plan__text {
  font-size: 14px;
  color: #80B8F5;
  font-weight: bold;
}

/* AI???? */
.ai-plan-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #F0F8FF;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ai-plan-header__text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.ai-plan-header__desc {
  font-size: 14px;
  color: #666666;
  text-align: center;
}

/* ?????????*/
.plan-modal {
  width: 90%;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  max-height: 80vh; /* ???????*/
  margin: 0 auto; /* ???? */
  position: relative; /* ?????? */
  z-index: 100000; /* ?????? */
}

.plan-modal__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #F0F8FF;
  border-bottom: 1px solid #E0E0E0;
}

.plan-modal__title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.plan-modal__close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}

.plan-modal__body {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.plan-modal__footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #E0E0E0;
}

/* AI?????? */
.ai-header {
  background-color: #E6F7FF;
}

.ai-intro {
  background-color: #F0F8FF;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.ai-intro__text {
  font-size: 14px;
  color: #1890FF;
  line-height: 1.5;
  display: flex;
  margin-bottom: 8px;
}

.ai-intro__note {
  font-size: 12px;
  color: #ff6b6b;
  line-height: 1.5;
  display: flex;
}

/* ???? */
.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}



.form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333333;
}

.form-textarea {
  width: 100%;
  height: 80px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 8px;
  font-size: 14px;
  color: #333333;
}

.time-range-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.picker-item {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  font-size: 14px;
  color: #333333;
}

.picker-separator {
  padding: 0 8px;
  color: #999999;
}

/* ????????*/
.plan-type-selector {
  display: flex;
  flex-direction: row;
  background-color: #F5F5F5;
  border-radius: 8px;
  overflow: hidden;
}

.plan-type-item {
  flex: 1;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.plan-type-item--active {
  background-color: #80B8F5;
}

.plan-type-item__text {
  font-size: 14px;
  color: #666666;
}

.plan-type-item--active .plan-type-item__text {
  color: #FFFFFF;
  font-weight: bold;
}

.plan-type-item--disabled {
  opacity: 0.7;
  pointer-events: none;
}

/* ???????*/
.priority-selector {
  display: flex;
  flex-direction: row;
  background-color: #F5F5F5;
  border-radius: 8px;
  overflow: hidden;
}

.priority-item {
  flex: 1;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.priority-item--low {
  background-color: #F5F5F5;
}

.priority-item--medium {
  background-color: #F5F5F5;
}

.priority-item--high {
  background-color: #F5F5F5;
}

.priority-item--active.priority-item--low {
  background-color: #52C41A;
}

.priority-item--active.priority-item--medium {
  background-color: #FAAD14;
}

.priority-item--active.priority-item--high {
  background-color: #FF4D4F;
}

.priority-item__text {
  font-size: 14px;
  color: #666666;
}

.priority-item--active .priority-item__text {
  color: #FFFFFF;
  font-weight: bold;
}


.reminder-text {
  font-size: 14px;
  color: #666666;
}

/* ????????*/
.learning-style-selector {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.learning-style-item {
  width: calc(50% - 16px);
  margin: 8px;
  padding: 12px;
  border-radius: 8px;
  background-color: #F5F5F5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.learning-style-item--active {
  background-color: #E6F7FF;
  border: 1px solid #1890FF;
}

.learning-style-item__icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.learning-style-item__text {
  font-size: 14px;
  color: #666666;
}

.learning-style-item--active .learning-style-item__text {
  color: #1890FF;
  font-weight: bold;
}

/* ????????*/
.skill-level-selector {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.skill-level-label {
  font-size: 12px;
  color: #999999;
  width: 50px;
}

/* ???? */
.btn-cancel {
  padding: 8px 16px;
  background-color: #F5F5F5;
  border-radius: 20px;
  font-size: 14px;
  color: #666666;
  margin-right: 12px;
  text-align: center;
}

.btn-generate {
  padding: 8px 16px;
  background-color: #80B8F5;
  border-radius: 20px;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
}

.btn-disabled {
  background-color: #CCCCCC !important;
  color: #FFFFFF !important;
  opacity: 0.7;
}

.btn-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-primary {
  padding: 8px 16px;
  background-color: #80B8F5;
  border-radius: 20px;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
}

.btn-edit {
  padding: 8px 16px;
  background-color: #F79F77;
  border-radius: 20px;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
}

/* ?????? */
.plan-detail-modal {
  width: 90%;
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
}

.plan-detail-modal__header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #F0F8FF;
  border-bottom: 1px solid #E0E0E0;
}

.plan-detail-modal__title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.plan-detail-modal__close {
  font-size: 24px;
  color: #999999;
  padding: 0 8px;
}

.plan-detail-modal__body {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.plan-detail-modal__footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #E0E0E0;
}

/* ?????? */
.plan-detail-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #F0F0F0;
}

.plan-detail-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}

.plan-detail-time {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4px;
  display: flex;
}

.plan-detail-date {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4px;
  display: flex;
}

.plan-detail-type {
  display: flex;
  padding: 4px 8px;
  background-color: #E6F7FF;
  border-radius: 4px;
  font-size: 12px;
  color: #1890FF;
  margin-top: 8px;
}

.plan-detail-description {
  margin-bottom: 16px;
}

.plan-detail-description__text {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
  display: flex;
}

/* ??????*/
.plan-detail-subtasks {
  margin-bottom: 16px;
}

.subtask-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.subtask-checkbox {
  width: 20px;
  height: 20px;
  border-radius: 999px;
  border: 2px solid #80B8F5;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
  position: relative;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.subtask-checkbox--checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
  box-shadow: 0 0 6px rgba(76, 175, 80, 0.3);
}




  100% { opacity: 1; transform: rotate(-45deg) scale(1); }
}

.subtask-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
  transition: background-color 0.3s ease;
}

.subtask-item--completed {
  background-color: #F9FFF9;
  padding-left: 8px;
  border-radius: 4px;
}

.subtask-content {
  flex: 1;
}

.subtask-title {
  font-size: 16px;
  color: #333333;
  margin-bottom: 4px;
  display: flex;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.subtask-title--completed {
  color: #999999;
  text-decoration: line-through;
  font-weight: normal;
}

.subtask-time {
  font-size: 12px;
  color: #999999;
  margin-bottom: 4px;
  display: flex;
}

.subtask-description {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

/* ?????? */
.resource-recommendations {
  margin-top: 16px;
}

.resource-list {
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
}

.resource-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #F0F0F0;
}

.resource-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.resource-icon--book {
  background-color: #FFE7BA;
}

.resource-icon--video {
  background-color: #FFD6E7;
}

.resource-icon--article {
  background-color: #D6E4FF;
}

.resource-icon--course {
  background-color: #D9F7BE;
}

.resource-icon--tool {
  background-color: #D3F8F4;
}

.resource-icon--audio {
  background-color: #EFD8FF;
}

.resource-icon__text {
  font-size: 20px;
}

.resource-content {
  flex: 1;
}

.resource-title {
  font-size: 16px;
  color: #333333;
  margin-bottom: 4px;
  display: flex;
}

.resource-desc {
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  display: flex;
  line-height: 1.4;
}

.resource-meta {
  display: flex;
  flex-direction: row;
}

.resource-difficulty {
  font-size: 12px;
  color: #999999;
  margin-right: 12px;
}

.resource-relevance {
  font-size: 12px;
  color: #999999;
}

.resource-arrow {
  font-size: 18px;
  color: #CCCCCC;
  margin-left: 8px;
}

/* ?????? */
.history-button {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  margin: 8px 0; /* ?????? */
  height: 28px; /* ???? */
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.history-button:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
}

.history-button__icon {
  font-size: 12px;
  color: #FFFFFF;
  margin-right: 4px;
}

.history-button__text {
  font-size: 12px;
  color: #FFFFFF;
  font-weight: bold;
}

.subtask-completion {
  font-size: 12px;
  color: #52C41A;
  margin-top: 4px;
  display: flex;
  font-style: italic;
  background-color: rgba(82, 196, 26, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  display: flex;
  margin-top: 8px;
}
</style>

