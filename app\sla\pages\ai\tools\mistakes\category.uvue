<template>
  <view class="mistakes-container">
    <!-- 顶部导航 -->
    <view class="mistakes-header">
      <view class="mistakes-back" @click="goBack">
        <text class="mistakes-back__icon">←</text>
      </view>
      <text class="mistakes-title">分类管理</text>
      <view class="mistakes-actions">
        <view class="mistakes-action" @click="showAddCategoryModal">
          <text class="mistakes-action__icon">+</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="mistakes-content" scroll-y>
      <!-- 分类列表 -->
      <view class="category-list">
        <view class="category-item" v-for="(category, index) in categories" :key="index">
          <view class="category-info">
            <text class="category-name">{{ category.name }}</text>
            <text class="category-count">{{ category.count }}个错题</text>
          </view>
          <view class="category-actions">
            <view class="category-action" @click="showEditCategoryModal(category)">
              <text class="category-action__icon">编辑</text>
            </view>
            <view class="category-action" @click="showDeleteConfirm(category)">
              <text class="category-action__icon">删除</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="categories.length === 0">
        <image class="empty-state__image" src="/static/images/empty-box.png" mode="aspectFit"></image>
        <text class="empty-state__text">暂无分类</text>
        <text class="empty-state__subtext">点击右上角"+"添加分类</text>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts">
import UniPopup from '../../../../uni_modules/uni-popup/components/uni-popup/uni-popup.uvue';
import { Category } from '../../../../utils/types';
import {
  getMistakeCategoryList,
  createMistakeCategory,
  updateMistakeCategory,
  deleteMistakeCategory
} from '../../../../utils/api/aitool.js';

// ??????
interface MistakeCategory {
  id: number;
  name: string;
  count: number;
  color?: string;
}

export default {
  components: {
    UniPopup
  },
  data() {
    return {
      categories: [] as MistakeCategory[],
      categoryName: '',
      selectedCategory: {} as MistakeCategory,
      isEditing: false,
      editingId: 0,
      loading: false
    }
  },
  onLoad() {
    this.loadCategories();
  },
  methods: {
    // ?????
    goBack() {
      uni.navigateBack();
    },

    // ??????
    loadCategories() {
      this.loading = true;

      // ??????
      uni.showLoading({
        title: '???...'
      });

      // ??API??????
      getMistakeCategoryList()
        .then(res => {
          this.categories = res.data || [];
        })
        .catch(err => {
          console.error('??????:', err);
          uni.showToast({
            title: '??????',
            icon: 'none'
          });
        })
        .finally(() => {
          this.loading = false;
          uni.hideLoading();
        });
    },

    // ????????
    showAddCategoryModal() {
      this.isEditing = false;
      this.categoryName = '';
      this.editingId = 0;

      uni.showModal({
        title: '????',
        placeholderText: '???????',
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            // ??????
            uni.showLoading({
              title: '???...'
            });

            // ??????
            const data = {
              name: res.content.trim(),
              color: '#' + Math.floor(Math.random()*16777215).toString(16) // ????
            };

            // ??API????
            createMistakeCategory(data)
              .then(res => {
                if (res.data && res.data.categoryId) {
                  // ??????
                  this.loadCategories();

                  uni.showToast({
                    title: '????',
                    icon: 'success'
                  });
                }
              })
              .catch(err => {
                console.error('??????:', err);
                uni.showToast({
                  title: '????',
                  icon: 'none'
                });
              })
              .finally(() => {
                uni.hideLoading();
              });
          }
        }
      });
    },

    // ????????
    showEditCategoryModal(category: MistakeCategory) {
      this.isEditing = true;
      this.categoryName = category.name;
      this.editingId = category.id;

      uni.showModal({
        title: '????',
        content: category.name,
        placeholderText: '?????????',
        editable: true,
        success: (res) => {
          if (res.confirm && res.content) {
            // ??????
            uni.showLoading({
              title: '???...'
            });

            // ??????
            const data = {
              name: res.content.trim(),
              color: category.color // ??????
            };

            // ??API????
            updateMistakeCategory(category.id, data)
              .then(res => {
                // ??????
                this.loadCategories();

                uni.showToast({
                  title: '????',
                  icon: 'success'
                });
              })
              .catch(err => {
                console.error('??????:', err);
                uni.showToast({
                  title: '????',
                  icon: 'none'
                });
              })
              .finally(() => {
                uni.hideLoading();
              });
          }
        }
      });
    },

    // ????????
    showDeleteConfirm(category: MistakeCategory) {
      this.selectedCategory = category;

      uni.showModal({
        title: '????',
        content: `?????"${category.name}"????????????????????`,
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            // ??????
            uni.showLoading({
              title: '???...'
            });

            // ??API????
            deleteMistakeCategory(category.id)
              .then(res => {
                // ??????
                this.loadCategories();

                uni.showToast({
                  title: '????',
                  icon: 'success'
                });
              })
              .catch(err => {
                console.error('??????:', err);
                uni.showToast({
                  title: '????',
                  icon: 'none'
                });
              })
              .finally(() => {
                uni.hideLoading();
              });
          }
        }
      });
    }
  }
}
</script>

<style>
@import './styles.css';

/* 分类管理页面特有样式 */
.category-list {
  padding: 8px 0;
}

.category-item {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.category-info {
  display: flex;
  flex-direction: column;
}

.category-name {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 4px;
}

.category-count {
  font-size: 14px;
  color: #666666;
}

.category-actions {
  display: flex;
  flex-direction: row;
}

.category-action {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.category-action__icon {
  font-size: 18px;
}

.popup-message {
  font-size: 16px;
  color: #666666;
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.5;
}

.popup-action--danger {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
}
</style>
</template>

