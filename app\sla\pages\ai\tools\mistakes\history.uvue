<template>
  <view class="mistakes-container">
    <!-- ????? -->
    <view class="mistakes-header">
      <view class="mistakes-back" @click="goBack">
        <text class="mistakes-back__icon">?</text>
      </view>
      <text class="mistakes-title">????</text>
    </view>

    <!-- ???? -->
    <scroll-view class="mistakes-content" scroll-y>
      <!-- ???? -->
      <view class="history-stats">
        <view class="stats-item">
          <text class="stats-item__number">{{ archivedMistakes.length }}</text>
          <text class="stats-item__label">?????</text>
        </view>
        <view class="stats-item">
          <text class="stats-item__number">{{ categoryCount }}</text>
          <text class="stats-item__label">????</text>
        </view>
        <view class="stats-item">
          <text class="stats-item__number">{{ getLatestArchiveDay() }}</text>
          <text class="stats-item__label">????</text>
        </view>
      </view>

      <!-- ???? -->
      <view class="history-list">
        <view class="section-header">
          <text class="section-title">????</text>
        </view>

        <!-- ?????? -->
        <view class="history-month" v-for="(group, month) in groupedMistakes" :key="month">
          <view class="month-header">
            <text class="month-title">{{ month }}</text>
            <text class="month-count">{{ group.length }}??</text>
          </view>

          <!-- ???? -->
          <view class="mistake-list">
            <view class="mistake-card" v-for="(mistake, index) in group" :key="index" @click="navigateToDetail(mistake)">
              <view class="mistake-content">
                <text class="mistake-text" :class="{'mistake-text--truncate': mistake.content.length > 100}">{{ mistake.content }}</text>
              </view>
              <view class="mistake-footer">
                <view class="mistake-info">
                  <text class="mistake-category">{{ mistake.categoryName }}</text>
                  <text class="mistake-time">{{ formatDate(mistake.archiveTime) }}</text>
                </view>
                <view class="mistake-tags">
                  <view class="mistake-tag" v-for="(point, pIndex) in mistake.knowledgePoints.slice(0, 1)" :key="pIndex">
                    <text class="mistake-tag__text">{{ point }}</text>
                  </view>
                  <text class="mistake-more" v-if="mistake.knowledgePoints.length > 1">+{{ mistake.knowledgePoints.length - 1 }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- ??? -->
      <view class="empty-state" v-if="archivedMistakes.length === 0">
        <image class="empty-state__image" src="/static/images/empty-box.png" mode="aspectFit"></image>
        <text class="empty-state__text">??????</text>
        <text class="empty-state__subtext">???????????</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getMistakeList } from '../../../../utils/api/aitool.js';

// ????
interface Mistake {
  id: number;
  categoryId: number;
  categoryName: string;
  image: string;
  content: string;
  type: string;
  analysis: string;
  difficulty: number;
  knowledgePoints: string[] | string;
  isArchived: boolean;
  createTime: string;
  archiveTime?: string;
}

export default {
  data() {
    return {
      archivedMistakes: [] as Mistake[],
      loading: false,
      pageNum: 1,
      pageSize: 50,
      hasMore: true
    }
  },
  computed: {
    // ????
    categoryCount(): number {
      const categories = new Set(this.archivedMistakes.map(m => m.categoryId));
      return categories.size;
    },

    // ???????
    groupedMistakes(): Record<string, Mistake[]> {
      const groups: Record<string, Mistake[]> = {};

      this.archivedMistakes.forEach(mistake => {
        const date = new Date(mistake.archiveTime || mistake.createTime);
        const monthKey = `${date.getFullYear()}?${date.getMonth() + 1}?`;

        if (!groups[monthKey]) {
          groups[monthKey] = [];
        }

        groups[monthKey].push(mistake);
      });

      // ???????????
      const sortedGroups: Record<string, Mistake[]> = {};
      Object.keys(groups)
        .sort((a, b) => {
          const yearA = parseInt(a.split('?')[0]);
          const yearB = parseInt(b.split('?')[0]);
          if (yearA !== yearB) return yearB - yearA;

          const monthA = parseInt(a.split('?')[1].split('?')[0]);
          const monthB = parseInt(b.split('?')[1].split('?')[0]);
          return monthB - monthA;
        })
        .forEach(key => {
          sortedGroups[key] = groups[key];
        });

      return sortedGroups;
    }
  },
  onLoad() {
    this.loadArchivedMistakes();
  },
  methods: {
    // ?????
    goBack() {
      uni.navigateBack();
    },

    // ?????????
    navigateToDetail(mistake: Mistake) {
      uni.navigateTo({
        url: `/pages/ai/tools/mistakes/detail?id=${mistake.id}`
      });
    },

    // ????????
    loadArchivedMistakes() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;

      // ??????
      uni.showLoading({
        title: '???...'
      });

      // ??????
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        isArchived: true
      };

      console.log('??????:', params);

      // ??API??????
      getMistakeList(params)
        .then(res => {
          console.log('??????:', res);

          // ???????????
          const processKnowledgePoints = (mistakes) => {
            return mistakes.map(mistake => {
              if (typeof mistake.knowledgePoints === 'string') {
                try {
                  mistake.knowledgePoints = JSON.parse(mistake.knowledgePoints);
                } catch (e) {
                  mistake.knowledgePoints = mistake.knowledgePoints ? [mistake.knowledgePoints] : [];
                }
              } else if (!Array.isArray(mistake.knowledgePoints)) {
                mistake.knowledgePoints = [];
              }
              return mistake;
            });
          };

          // ???????????
          if (res.data && res.data.records) {
            // ??API???? {records, total, size, current, pages}
            if (this.pageNum === 1) {
              this.archivedMistakes = processKnowledgePoints(res.data.records);
            } else {
              this.archivedMistakes = [...this.archivedMistakes, ...processKnowledgePoints(res.data.records)];
            }
            this.hasMore = this.archivedMistakes.length < res.data.total;
          } else if (res.data && res.data.list) {
            // ??API???? {list, total}
            if (this.pageNum === 1) {
              this.archivedMistakes = processKnowledgePoints(res.data.list);
            } else {
              this.archivedMistakes = [...this.archivedMistakes, ...processKnowledgePoints(res.data.list)];
            }
            this.hasMore = this.archivedMistakes.length < res.data.total;
          } else {
            console.warn('???API????:', res.data);
            if (this.pageNum === 1) {
              this.archivedMistakes = [];
            }
            this.hasMore = false;
          }
        })
        .catch(err => {
          console.error('????????:', err);
          uni.showToast({
            title: '????',
            icon: 'none'
          });

          // ?????????
          if (this.pageNum > 1) {
            this.pageNum--;
          }
        })
        .finally(() => {
          this.loading = false;
          uni.hideLoading();
        });
    },

    // ????
    onPullDownRefresh() {
      this.pageNum = 1;
      this.hasMore = true;
      this.loadArchivedMistakes();

      setTimeout(() => {
        uni.stopPullDownRefresh();
      }, 1000);
    },

    // ????
    onReachBottom() {
      if (this.hasMore && !this.loading) {
        this.pageNum++;
        this.loadArchivedMistakes();
      }
    },

    // ????????
    getLatestArchiveDay(): string {
      if (this.archivedMistakes.length === 0) {
        return '0?';
      }

      // ????????
      const latestDate = new Date(Math.max(...this.archivedMistakes.map(
        m => new Date(m.archiveTime || m.createTime).getTime()
      )));

      const now = new Date();
      const diffTime = now.getTime() - latestDate.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return '??';
      } else if (diffDays === 1) {
        return '??';
      } else {
        return `${diffDays}??`;
      }
    },

    // ??????????
    formatDate(timeString: string): string {
      const date = new Date(timeString);
      return `${date.getMonth() + 1}?${date.getDate()}?`;
    }
  }
}
</script>

<style>
@import './styles.css';

/* ????????*/
.history-stats {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-item__number {
  font-size: 24px;
  font-weight: bold;
  color: #FF3B30;
  margin-bottom: 4px;
}

.stats-item__label {
  font-size: 14px;
  color: #666666;
}

.history-list {
  margin-bottom: 20px;
}

.month-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.month-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.month-count {
  font-size: 14px;
  color: #999999;
}

.history-month {
  margin-bottom: 24px;
}

.mistake-info {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.mistake-category {
  font-size: 12px;
  color: #FF3B30;
  background-color: rgba(255, 59, 48, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 8px;
}

.mistake-time {
  font-size: 12px;
  color: #999999;
}
</style>

