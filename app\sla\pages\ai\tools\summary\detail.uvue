<template>
  <view class="detail-container">
    <!-- 顶部导航栏 -->
    <view class="detail-header">
      <view class="detail-back" @click="goBack">
        <text class="detail-back__icon">←</text>
      </view>
      <text class="detail-title">总结详情</text>
      <view class="detail-actions">
        <view class="detail-action" @click="confirmDelete">
          <text class="detail-action__icon">🗑️</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="detail-content" scroll-y>
      <!-- 加载中 -->
      <view class="loading-state" v-if="loading">
        <view class="loading-animation">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 总结详情 -->
      <view class="summary-detail" v-if="!loading && summaryDetail">
        <!-- 标题 -->
        <view class="summary-title">
          <text class="summary-title__text">{{ summaryDetail.title }}</text>
        </view>

        <!-- 元信息 -->
        <view class="summary-meta">
          <view class="summary-meta__item">
            <text class="summary-meta__label">创建时间：</text>
            <text class="summary-meta__value">{{ formatTime(summaryDetail.createdTime) }}</text>
          </view>
          <view class="summary-meta__item" v-if="summaryDetail.originalFileName">
            <text class="summary-meta__label">原始文件：</text>
            <text class="summary-meta__value">{{ summaryDetail.originalFileName }}</text>
          </view>
        </view>

        <!-- 关键词 -->
        <view class="summary-keywords" v-if="summaryDetail.keywords && summaryDetail.keywords.length > 0">
          <text class="summary-keywords__title">关键词</text>
          <view class="summary-keywords__list">
            <text class="keyword-tag" v-for="(keyword, index) in summaryDetail.keywords" :key="index">{{ keyword }}</text>
          </view>
        </view>

        <!-- 总结内容 -->
        <view class="summary-content">
          <view class="summary-section-header">
            <text class="summary-content__title">总结内容</text>
            <view class="summary-action-btn" @click="copySummary">
              <text class="summary-action-btn__text">复制文本</text>
            </view>
          </view>

          <!-- 使用rich-text渲染Markdown内容 -->
          <rich-text class="summary-content__markdown" :nodes="formattedSummary"></rich-text>
        </view>

        <!-- 思维导图 -->
        <view class="summary-mindmap" v-if="summaryDetail.mindmapUrl">
          <view class="summary-section-header">
            <text class="summary-mindmap__title">思维导图</text>
            <view class="summary-action-btn" @click="copyMindmapUrl">
              <text class="summary-action-btn__text">复制链接</text>
            </view>
          </view>
          <image class="summary-mindmap__image" :src="summaryDetail.mindmapUrl" mode="widthFix" @click="previewMindmap"></image>
        </view>
      </view>

      <!-- 错误状态 -->
      <view class="error-state" v-if="!loading && error">
        <view class="error-icon">
          <text class="error-icon__text">⚠️</text>
        </view>
        <text class="error-text">加载失败</text>
        <text class="error-desc">{{ error }}</text>
        <view class="error-action" @click="retryLoading">
          <text class="error-action__text">重新加载</text>
        </view>
        <view class="error-action" @click="goToHistory">
          <text class="error-action__text">返回列表</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getSummaryDetail, deleteSummary } from '../../../../utils/api/aitool.js';
import { showSuccess, showError } from '../../../../utils/notificationManager.js';

export default {
  data() {
    return {
      summaryId: '' as string,
      summaryDetail: {
        id: 0,
        title: '',
        summary: '',
        keywords: [] as string[],
        mindmapUrl: '',
        originalFileName: '',
        createdTime: '',
        contentType: '',
        relatedId: ''
      },
      isShared: false,
      loading: true,
      error: '',
      statusBarHeight: 0,
      menuButtonInfo: {
        top: 0,
        height: 0,
        width: 0
      },
      formattedSummary: ''
    }
  },
  onLoad(options: Record<string, any>) {
    console.log('加载参数:', options);
    
    // 获取总结ID
    if (options && (options.id || options.summaryId)) {
      this.summaryId = String(options.id || options.summaryId || '');
      console.log('总结记录ID:', this.summaryId);
      // 加载详情
      this.loadSummaryDetail();
    } else {
      console.error('未能获取到总结ID参数:', options);
      this.error = '缺少总结ID';
      this.loading = false;
    }
  },
  methods: {
    // 返回上级
    goBack() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          // 如果无法返回上一页，则跳转到总结首页
          uni.redirectTo({
            url: '/pages/ai/tools/summary/index'
          });
        }
      });
    },

    // 加载详情
    loadSummaryDetail() {
      if (!this.summaryId) {
        this.error = '总结ID不存在，无法加载详情';
        this.loading = false;
        return;
      }

      this.loading = true;
      this.error = '';

      console.log('正在加载总结ID:', this.summaryId);
      getSummaryDetail(this.summaryId).then(response => {
        console.log('总结详情响应:', response);

        // 检查响应是否存在
        if (!response) {
          throw new Error('未收到响应数据');
        }

        // 获取响应对象
        const res = response;

        // 服务不可用处理
        if (res.statusCode === 503) {
          console.error('服务暂时不可用，状态码:', res.statusCode);
          throw new Error('服务暂时不可用，请稍后再试');
        }

        // 处理成功响应并解析数据
        if (res && res.data && res.data.code === 200 && res.data.data) {
          // 设置总结详情
          this.summaryDetail = res.data.data;
          console.log('总结详情数据:', this.summaryDetail);

          // 处理keywords可能是字符串格式的JSON的情况
          if (this.summaryDetail) {
            // 尝试解析可能是JSON字符串的keywords
            if (typeof this.summaryDetail.keywords === 'string') {
              try {
                const parsedKeywords = JSON.parse(this.summaryDetail.keywords);
                if (Array.isArray(parsedKeywords)) {
                  this.summaryDetail.keywords = parsedKeywords;
                } else {
                  this.summaryDetail.keywords = [];
                }
              } catch (e) {
                console.warn('关键词解析失败', e);
                this.summaryDetail.keywords = [];
              }
            } else if (!Array.isArray(this.summaryDetail.keywords)) {
              this.summaryDetail.keywords = [];
            }
          }

          // 格式化Markdown内容
          this.formatMarkdown();
        } else {
          console.error('获取总结详情响应数据错误:', res);
          throw new Error(res.data?.message || '获取总结详情失败，请稍后重试');
        }
      }).catch(err => {
        console.error('加载详情失败:', err);
        this.error = err.message || '加载失败，请检查网络连接并重试';
      }).finally(() => {
        this.loading = false;
      });
    },

    // 重试加载
    retryLoading() {
      this.loadSummaryDetail();
    },

    // 返回历史
    goToHistory() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.redirectTo({
            url: '/pages/ai/tools/summary/history'
          });
        }
      });
    },

    // 格式化Markdown内容
    formatMarkdown() {
      try {
        if (!this.summaryDetail || !this.summaryDetail.summary) {
          this.formattedSummary = '';
          return;
        }

        let formatted = this.summaryDetail.summary;

        // 处理段落，将连续两个换行符转换为段落标签
        formatted = formatted.replace(/\n\n/g, '</p><p>');
        formatted = '<p>' + formatted + '</p>';

        // 将单个换行符转换为<br>标签
        formatted = formatted.replace(/\n/g, '<br>');

        // 处理Markdown样式语法： **text** 转换为 <strong>text</strong>
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 处理Markdown样式语法： *text* 转换为 <em>text</em>
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

        this.formattedSummary = formatted;
      } catch (error) {
        console.error('格式化Markdown内容出错:', error);
        // 最简单的后备格式化，只处理换行
        this.formattedSummary = this.summaryDetail.summary.replace(/\n/g, '<br>');
      }
    },

    // 复制总结内容
    copySummary() {
      if (!this.summaryDetail || !this.summaryDetail.summary) {
        uni.showToast({
          title: '没有可复制的内容',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: this.summaryDetail.summary,
        success: () => {
          uni.showToast({
            title: '总结内容已复制到剪贴板',
            icon: 'success'
          });
        }
      });
    },

    // 复制思维导图链接
    copyMindmapUrl() {
      if (!this.summaryDetail || !this.summaryDetail.mindmapUrl) {
        uni.showToast({
          title: '思维导图链接不存在',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: this.summaryDetail.mindmapUrl,
        success: () => {
          uni.showToast({
            title: '链接已复制成功',
            icon: 'success'
          });
        }
      });
    },

    // 预览思维导图
    previewMindmap() {
      if (!this.summaryDetail || !this.summaryDetail.mindmapUrl) return;

      uni.previewImage({
        urls: [this.summaryDetail.mindmapUrl],
        current: this.summaryDetail.mindmapUrl,
        fail: (err) => {
          console.error('预览失败:', err);
          
          // 如果预览失败，则尝试打开网页
          uni.showModal({
            title: '提示',
            content: '是否在网页中打开思维导图？',
            success: (res) => {
              if (res.confirm) {
                // 使用网页查看
                uni.navigateTo({
                  url: `/pages/webview/index?url=${encodeURIComponent(this.summaryDetail.mindmapUrl)}`
                });
              }
            }
          });
        }
      });
    },

    // 确认删除
    confirmDelete() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除此总结记录吗？删除后不可恢复',
        success: (res) => {
          if (res.confirm) {
            this.deleteSummary();
          }
        }
      });
    },

    // 删除总结
    deleteSummary() {
      if (!this.summaryId) {
        showError('操作失败', '总结ID不存在');
        return;
      }

      uni.showLoading({
        title: '删除中...'
      });

      deleteSummary(this.summaryId).then(response => {
        console.log('删除响应:', response);

        // 获取响应对象
        const res = response;

        // 处理成功响应并解析数据
        if (res && res.data && res.data.code === 200) {
          // 删除成功提示
          showSuccess('删除成功');

          // 返回上级页面
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error((res.data && res.data.message) || '删除失败');
        }
      }).catch(err => {
        console.error('删除失败:', err);
        showError('删除失败', err.message || '操作失败');
      }).finally(() => {
        uni.hideLoading();
      });
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';

      try {
        const date = new Date(timeStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (e) {
        return timeStr;
      }
    }
  }
}
</script>

<style>
.detail-container {
  min-height: 800px;
  background-color: #F8F9FA;
  display: flex;
  flex-direction: column;
}

.detail-header {
  height: 56px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(74, 123, 219, 0.2);
  position: relative;
}

.detail-back {
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-back__icon {
  font-size: 20px;
  color: #FFFFFF;
  font-weight: bold;
}

.detail-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
  width: 100%;
  position: absolute;
  left: 0;
}

.detail-actions {
  position: absolute;
  right: 16px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 40px;
  display: flex;
  align-items: center;
}

.detail-action {
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-action__icon {
  font-size: 20px;
  color: #FFFFFF;
}

.detail-content {
  flex: 1;
  padding: 16px;
}

/* 加载中状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 16px;
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.loading-dot {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  margin: 0 5px;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-text {
  font-size: 16px;
  color: #4A7BDB;
  font-weight: bold;
}

/* 总结详情 */
.summary-detail {
  margin-bottom: 24px;
}

/* 标题 */
.summary-title {
  margin-bottom: 16px;
}

.summary-title__text {
  font-size: 22px;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
}

/* 元信息 */
.summary-meta {
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-meta__item {
  display: flex;
  flex-direction: row;
  margin-bottom: 8px;
}

.summary-meta__label {
  font-size: 14px;
  color: #666666;
  min-width: 70px;
}

.summary-meta__value {
  font-size: 14px;
  color: #333333;
  flex: 1;
}

/* 关键词 */
.summary-keywords {
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-keywords__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.summary-keywords__list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin: -4px;
}

.keyword-tag {
  margin: 4px;
  padding: 6px 12px;
  background-color: #F0F4FF;
  border-radius: 16px;
  font-size: 14px;
  color: #4A7BDB;
}

/* 总结内容 */
.summary-content {
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-section-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #F0F0F0;
}

.summary-content__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.summary-action-btn {
  padding: 6px 12px;
  border-radius: 16px;
  background-color: #F0F0F0;
}

.summary-action-btn__text {
  font-size: 14px;
  color: #666666;
}

.summary-content__markdown {
  line-height: 1.6;
  font-size: 16px;
  color: #333333;
}

/* 思维导图 */
.summary-mindmap {
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-mindmap__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.summary-mindmap__image {
  width: 100%;
  margin-top: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 16px;
}

.error-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: rgba(244, 67, 54, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.error-icon__text {
  font-size: 30px;
  color: #F44336;
}

.error-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
}

.error-desc {
  font-size: 14px;
  color: #666666;
  margin-bottom: 24px;
  text-align: center;
}

.error-action {
  padding: 12px 24px;
  background-color: #F0F4FF;
  border-radius: 22px;
  margin-bottom: 12px;
  border: 1px solid #4A7BDB;
}

.error-action__text {
  font-size: 14px;
  font-weight: bold;
  color: #4A7BDB;
}
</style>
</template>

