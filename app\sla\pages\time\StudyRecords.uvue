<template>
  <view class="records-container">
    <!-- ????? -->
    <sla-navbar title="????" :show-back="true">
      <template #left>
        <view class="back-button" @click="handleBack">
          <text class="back-button-icon">?</text>
        </view>
      </template>
      <template #right>
        <view class="action-button" @click="clearAllRecords">
          <text class="action-button__text">??</text>
        </view>
      </template>
    </sla-navbar>

    <!-- ???? -->
    <view class="stats-card">
      <view class="stats-item">
        <text class="stats-value">{{ totalStudyTime }}</text>
        <text class="stats-label">???(??)</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ studyRecords.length }}</text>
        <text class="stats-label">????</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{ averageStudyTime }}</text>
        <text class="stats-label">????(??)</text>
      </view>
    </view>

    <!-- ??? -->
    <view class="filter-bar">
      <view class="filter-item" :class="{'filter-item--active': timeFilter === 'all'}" @click="setTimeFilter('all')">
        <text class="filter-text">??</text>
      </view>
      <view class="filter-item" :class="{'filter-item--active': timeFilter === 'today'}" @click="setTimeFilter('today')">
        <text class="filter-text">??</text>
      </view>
      <view class="filter-item" :class="{'filter-item--active': timeFilter === 'week'}" @click="setTimeFilter('week')">
        <text class="filter-text">??</text>
      </view>
      <view class="filter-item" :class="{'filter-item--active': timeFilter === 'month'}" @click="setTimeFilter('month')">
        <text class="filter-text">??</text>
      </view>
    </view>

    <!-- ???? -->
    <scroll-view class="records-list" scroll-y>
      <view v-if="filteredRecords.length === 0" class="empty-state">
        <text class="empty-text">??????</text>
      </view>
      <view v-else>
        <!-- ????? -->
        <view v-for="(group, date) in groupedRecords" :key="date" class="record-group">
          <view class="record-date">
            <text class="record-date__text">{{ formatDateHeader(date) }}</text>
            <view class="record-date__line"></view>
          </view>

          <view v-for="record in group" :key="record.id" class="record-item">
            <view class="record-time">
              <text class="record-time__text">{{ formatRecordTime(record.date) }}</text>
              <text class="record-duration">{{ record.duration }}??</text>
            </view>
            <view class="record-card">
              <view class="record-content">
                <view class="record-info">
                  <text class="record-room">{{ record.roomName }}</text>
                  <text class="record-environment">{{ record.environment }}</text>
                </view>
                <view class="record-actions">
                  <view class="record-delete" @click.stop="deleteRecord(record.id)">
                    <text class="record-delete__icon">???</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import SlaNavbar from '../../components/user/SlaNavbar.uvue';

// ??????
interface StudyRecord {
  id: number;
  date: string;
  duration: number;
  roomName: string;
  environment: string;
}

export default {
  components: {
    SlaNavbar
  },
  data() {
    return {
      studyRecords: [] as StudyRecord[],
      timeFilter: 'all' as string // 'all', 'today', 'week', 'month'
    }
  },
  computed: {
    // ?????????
    filteredRecords(): StudyRecord[] {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      return this.studyRecords.filter(record => {
        const recordDate = this.parseDate(record.date);
        if (!recordDate) return false;

        switch (this.timeFilter) {
          case 'today':
            return recordDate >= today;
          case 'week':
            return recordDate >= weekStart;
          case 'month':
            return recordDate >= monthStart;
          default:
            return true;
        }
      });
    },

    // ????????
    groupedRecords(): Record<string, StudyRecord[]> {
      const groups: Record<string, StudyRecord[]> = {};

      this.filteredRecords.forEach(record => {
        const dateStr = this.getDateOnly(record.date);
        if (!groups[dateStr]) {
          groups[dateStr] = [];
        }
        groups[dateStr].push(record);
      });

      // ?????????
      const sortedGroups: Record<string, StudyRecord[]> = {};
      Object.keys(groups)
        .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
        .forEach(key => {
          sortedGroups[key] = groups[key];
        });

      return sortedGroups;
    },

    // ?????????
    totalStudyTime(): number {
      return this.filteredRecords.reduce((sum, record) => sum + record.duration, 0);
    },

    // ??????????
    averageStudyTime(): number {
      if (this.filteredRecords.length === 0) return 0;
      return Math.round(this.totalStudyTime / this.filteredRecords.length);
    }
  },
  onLoad() {
    this.loadStudyRecords();
  },
  onShow() {
    // ?????????????
    this.loadStudyRecords();
  },
  methods: {
    // ????????
    handleBack() {
      try {
        // ??uni-app?API?????
        uni.navigateBack({
          delta: 1
        });
      } catch (e) {
        console.error('???????', e);
        // ??????????switchTab???????
        uni.switchTab({
          url: '/pages/time/index'
        });
      }
    },

    // ??????
    loadStudyRecords() {
      try {
        const records = uni.getStorageSync('studyRecords');
        if (records) {
          try {
            this.studyRecords = JSON.parse(records);
            // ???????
            this.studyRecords.sort((a, b) => {
              return new Date(b.date).getTime() - new Date(a.date).getTime();
            });
          } catch (parseError) {
            console.error('????????:', parseError);
            this.studyRecords = [];
            // ?????????
            uni.setStorageSync('studyRecords', '[]');
          }
        } else {
          this.studyRecords = [];
        }
      } catch (e) {
        console.error('????????:', e);
        this.studyRecords = [];
      }
    },

    // ????
    parseDate(dateStr: string): Date | null {
      if (!dateStr) return null;

      // ??"?? HH:MM"??
      if (dateStr.startsWith('??')) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const timeStr = dateStr.replace('??', '').trim();
        const [hours, minutes] = timeStr.split(':').map(Number);
        today.setHours(hours, minutes, 0, 0);
        return today;
      }

      // ??"?? HH:MM"??
      if (dateStr.startsWith('??')) {
        const now = new Date();
        const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        const timeStr = dateStr.replace('??', '').trim();
        const [hours, minutes] = timeStr.split(':').map(Number);
        yesterday.setHours(hours, minutes, 0, 0);
        return yesterday;
      }

      // ??????????
      return new Date(dateStr);
    },

    // ???????
    getDateOnly(dateStr: string): string {
      const date = this.parseDate(dateStr);
      if (!date) return dateStr;

      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // ???????
    setTimeFilter(filter: string) {
      this.timeFilter = filter;
    },

    // ?????????
    formatRecordTime(dateStr: string): string {
      if (dateStr.includes(':')) {
        return dateStr.split(' ')[1];
      }
      return dateStr;
    },

    // ???????
    formatDateHeader(dateStr: string): string {
      const date = new Date(dateStr);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.getFullYear() === today.getFullYear() &&
          date.getMonth() === today.getMonth() &&
          date.getDate() === today.getDate()) {
        return '??';
      }

      if (date.getFullYear() === yesterday.getFullYear() &&
          date.getMonth() === yesterday.getMonth() &&
          date.getDate() === yesterday.getDate()) {
        return '??';
      }

      return `${date.getFullYear()}?${date.getMonth() + 1}?${date.getDate()}?`;
    },

    // ??????
    deleteRecord(id: number) {
      uni.showModal({
        title: '????',
        content: '?????????????',
        success: (res) => {
          if (res.confirm) {
            try {
              const index = this.studyRecords.findIndex(record => record.id === id);
              if (index !== -1) {
                // ????????
                this.studyRecords.splice(index, 1);

                // ????
                uni.setStorageSync('studyRecords', JSON.stringify(this.studyRecords));

                uni.showToast({
                  title: '????',
                  icon: 'success'
                });
              }
            } catch (error) {
              console.error('??????:', error);
              uni.showToast({
                title: '????',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 清空所有记录
    clearAllRecords() {
      uni.showModal({
        title: '清空记录',
        content: '确定要清空所有学习记录吗？此操作不可恢复！',
        success: (res) => {
          if (res.confirm) {
            try {
              // 清空内存中的记录
              this.studyRecords = [];

              // 清空本地存储中的记录
              uni.setStorageSync('studyRecords', '[]');

              uni.showToast({
                title: '已清空所有记录',
                icon: 'success'
              });
            } catch (error) {
              console.error('清空记录失败:', error);
              uni.showToast({
                title: '清空记录失败',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
}
</script>

<style>
/* 容器样式 */
.records-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #eef2ff, #f5f7fa);
}

/* 返回按钮样式 */
.back-button {
  padding: 8px;
  margin-left: -8px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.back-button:active {
  transform: scale(0.92);
}

.back-button-icon {
  font-size: 24px;
  color: #5B7FFF;
  font-weight: normal;
}

/* 顶部操作按钮 */
.action-button {
  padding: 6px 12px;
  border-radius: 12px;
  background-color: rgba(255, 77, 79, 0.1);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.action-button:active {
  transform: scale(0.92);
}

.action-button__text {
  font-size: 14px;
  color: #FF4D4F;
  font-weight: bold;
}

/* 统计卡片 */
.stats-card {
  margin: 16px;
  background-color: #FFFFFF;
  border-radius: 14px;
  padding: 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  position: relative;
  overflow: hidden;
}



.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.stats-item:hover {
  transform: translateY(-2px);
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  color: #5B7FFF;
  margin-bottom: 6px;
}

.stats-label {
  font-size: 13px;
  color: #5E6C84;
  font-weight: bold;
}

/* 过滤器*/
.filter-bar {
  display: flex;
  flex-direction: row;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 14px;
  margin: 0 16px 16px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(31, 60, 136, 0.05);
  padding: 3px;

}

.filter-item {
  flex: 1;
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.filter-item--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.filter-text {
  font-size: 14px;
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.filter-item--active .filter-text {
  color: #FFFFFF;
  font-weight: bold;
}

/* 记录列表 */
.records-list {
  flex: 1;
  padding: 0 16px;
  margin-bottom: 16px;
}

.record-group {
  margin-bottom: 16px;
}

.record-date {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.record-date__text {
  font-size: 14px;
  font-weight: bold;
  color: #2E3A59;
  margin-right: 10px;
  flex-shrink: 0;
  letter-spacing: 0.2px;
}

.record-date__line {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, rgba(91, 127, 255, 0.3), rgba(128, 184, 245, 0.1));
}

.record-item {
  margin-bottom: 16px;
}

.record-time {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 6px;
  padding: 0 8px;
}

.record-time__text {
  font-size: 13px;
  color: #5E6C84;
  font-weight: bold;
}

.record-duration {
  font-size: 13px;
  color: #5B7FFF;
  font-weight: bold;
  background-color: rgba(91, 127, 255, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

.record-card {
  background-color: #FFFFFF;
  border-radius: 14px;
  padding: 16px;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
  overflow: hidden;
}



.record-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(31, 60, 136, 0.08);
}

.record-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.record-info {
  flex: 1;
  padding-left: 8px;
}

.record-room {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  margin-bottom: 6px;
  display: flex;
  letter-spacing: 0.2px;
}

.record-environment {
  font-size: 13px;
  color: #5E6C84;
  display: flex;
  font-weight: bold;
}

.record-actions {
  margin-left: 12px;
}

.record-delete {
  width: 32px;
  height: 32px;
  border-radius: 999px;
  background-color: rgba(255, 77, 79, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.record-delete:active {
  transform: scale(0.92);
  background-color: rgba(255, 77, 79, 0.2);
}

.record-delete__icon {
  font-size: 18px;
  color: #FF4D4F;
}

/* 空状态*/
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 0;
}

.empty-text {
  font-size: 16px;
  color: #5E6C84;
  text-align: center;
  font-weight: bold;
}
</style>
</template>
