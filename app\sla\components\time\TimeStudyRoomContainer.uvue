<template>
  <view class="study-room-container">
    <!-- ĺčĄ¨ć¨Ąĺź -->
    <view v-if="studyRoomMode === 'list'">
      <study-room-list-new @select-room="handleSelectRoom"></study-room-list-new>
    </view>
    
    <!-- čŞäš ĺŽ¤čŻŚćć¨Ąĺź?-->
    <view v-else-if="studyRoomMode === 'study'" class="room-detail">
      <navigator :url="'/pages/time/TimeStudyRoomDetail?roomId=' + currentStudyRoom.roomId" open-type="navigate">
        <view class="room-card">
          <view class="room-header">
            <view class="room-title">
              <text class="room-name">{{ currentStudyRoom.name }}</text>
              <view class="room-status">
                <text class="status-dot" :class="'status-' + currentStatus"></text>
                <text class="status-text">{{ getStatusText(currentStatus) }}</text>
              </view>
            </view>
            <view class="room-members">
              <text class="iconfont icon-user"></text>
              <text>{{ currentStudyRoom.currentUsers || currentStudyRoom.currentMembers || 1 }}/{{ currentStudyRoom.capacity || 8 }}</text>
            </view>
          </view>
          
          <view class="room-environment">
            <text class="env-label">çŻĺ˘ďź?/text>
            <text class="env-value">{{ getEnvName(currentStudyRoom.environment) }}</text>
          </view>
          
          <view class="room-actions">
            <button class="focus-btn" @click.stop="startFocus">ĺźĺ§ä¸ćł?/button>
            <button class="enter-btn">čżĺĽčŞäš ĺŽ?/button>
          </view>
        </view>
      </navigator>
      
      <view class="back-to-list" @click="backToList">
        <text class="back-text">čżĺčŞäš ĺŽ¤ĺčĄ?/text>
      </view>
    </view>
  </view>
</template>

<script>
import StudyRoomListNew from './StudyRoomListNew.uvue';
// ĺŻźĺĽčŞäš ĺŽ¤API
import { getStudyRoomDetail, exitStudyRoom, joinStudyRoom } from '../../utils/api/time.js';

interface StudyRoom {
  roomId: string;
  name: string;
  description: string;
  capacity: number;
  currentMembers: number;
  currentUsers?: number;
  isPublic: boolean;
  tags: string[];
  environment: string;
  backgroundMusic: string;
  isJoined: boolean;
  creatorName: string;
  createTime: string;
}

interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

export default {
  components: {
    StudyRoomListNew
  },
  data() {
    return {
      studyRoomMode: 'list', // 'list' ć?'study'
      currentStudyRoom: null as StudyRoom | null, // ĺ˝ĺčŞäš ĺŽ?      currentStatus: 'focusing', // ĺ˝ĺçść?      isLoading: false // ćŻĺŚć­Łĺ¨ĺ č˝˝
    };
  },
  created() {
    // çĺŹčŞäš ĺŽ¤č§ŁćŁäşäť?    uni.$on('studyRoomDisbanded', this.handleStudyRoomDisbanded);
    
    // çĺŹčŞäš ĺŽ¤ĺčĄ¨ĺˇć°äşäť?    uni.$on('refreshStudyRoomList', this.refreshRoomList);
  },
  onShow() {
    // ćŁćĽćŻĺŚćäżĺ­çćżé´IDĺšśĺ°čŻĺ ĺ?    this.checkSavedRoom();
    
    // ćŁćĽćŻĺŚéčŚĺˇć°ĺčĄ?    try {
      const needRefresh = uni.getStorageSync('needRefreshStudyRoomList');
      if (needRefresh) {
        // ć¸é¤ć čŽ°
        uni.removeStorageSync('needRefreshStudyRoomList');
        // ĺŚćĺ¨ĺčĄ¨ć¨ĄĺźďźéçĽĺčĄ¨çťäťśĺˇć°
        if (this.studyRoomMode === 'list') {
          uni.$emit('refreshStudyRoomList');
        }
      }
    } catch (e) {
      console.error('ćŁćĽĺˇć°ć čŽ°ĺ¤ąč´?, e);
    }
  },
  beforeDestroy() {
    // ĺćśçĺŹ
    uni.$off('studyRoomDisbanded', this.handleStudyRoomDisbanded);
    uni.$off('refreshStudyRoomList', this.refreshRoomList);
  },
  methods: {
    // ćŁćĽćŹĺ°äżĺ­çćżé´IDĺšśčŞĺ¨ĺ ĺ?    async checkSavedRoom() {
      try {
        const lastJoinedRoom = uni.getStorageSync('lastJoinedRoom');
        console.log('ćŁćĽäżĺ­çčŞäš ĺŽ¤ID:', lastJoinedRoom);
        
        if (lastJoinedRoom) {
          // ćžç¤şĺ č˝˝ćç¤ş
          uni.showLoading({
            title: 'ć­Łĺ¨čˇĺčŞäš ĺŽ¤äżĄć?..',
            mask: true
          });
          
          // čˇĺčŞäš ĺŽ¤čŻŚć?          try {
            const response = await getStudyRoomDetail(lastJoinedRoom);
            console.log('čˇĺäżĺ­çčŞäš ĺŽ¤čŻŚć:', response);
            
            if (response && response.code === 200 && response.data) {
              // ćżé´ĺ­ĺ¨ďźčŞĺ¨ĺ ĺ?              uni.showToast({
                title: 'ć­Łĺ¨čżĺĽć¨çčŞäš ĺŽ?..',
                icon: 'none',
                duration: 2000
              });
              
              // čŞĺ¨ĺ ĺĽćżé´
              this.joinRoom(lastJoinedRoom);
              return true;
            } else {
              // ćżé´ä¸ĺ­ĺ¨ďźć¸é¤çźĺ­
              console.log('äżĺ­çčŞäš ĺŽ¤ä¸ĺ­ĺ¨ďźć¸é¤çźĺ­');
              uni.removeStorageSync('lastJoinedRoom');
              uni.hideLoading();
              return false;
            }
          } catch (error) {
            console.error('čˇĺčŞäš ĺŽ¤čŻŚćĺ¤ąč´?', error);
            uni.hideLoading();
            // ć¸é¤ĺŻč˝ĺ¤ąćçćżé´ID
            uni.removeStorageSync('lastJoinedRoom');
            return false;
          }
        }
        return false;
      } catch (error) {
        console.error('ćŁćĽäżĺ­çčŞäš ĺŽ¤IDĺşé:', error);
        return false;
      }
    },
    
    // ĺ ĺĽćżé´
    async joinRoom(roomId) {
      if (this.isLoading) return;
      this.isLoading = true;
      
      try {
        // ćžç¤şĺ č˝˝ćç¤ş
        uni.showLoading({
          title: 'ć­Łĺ¨ĺ ĺĽčŞäš ĺŽ?..',
          mask: true
        });
        
        // č°ç¨ĺ ĺĽčŞäš ĺŽ¤API
        const response = await joinStudyRoom({ roomId });
        console.log('ĺ ĺĽčŞäš ĺŽ¤ĺĺş?', response);
        
        if (response && response.code === 200) {
          // ĺ ĺĽćĺďźčˇĺčŻŚć?          const detailResponse = await getStudyRoomDetail(roomId);
          console.log('čˇĺčŞäš ĺŽ¤čŻŚć?', detailResponse);
          
          if (detailResponse && detailResponse.code === 200) {
            // čŽžç˝Žĺ˝ĺčŞäš ĺŽ?            this.currentStudyRoom = detailResponse.data;
            
            // çźĺ­ćżé´äżĄćŻ
            uni.setStorageSync('cachedRoom_' + roomId, JSON.stringify(this.currentStudyRoom));
            
            // ĺć˘ĺ°čŞäš ĺŽ¤ć¨Ąĺź
            this.studyRoomMode = 'study';
            
            // ćŁćĽćŻĺŚćŻĺĺťşččżĺ?            const isCreator = uni.getStorageSync('lastJoinedRoom') === roomId;
            console.log('ćŻĺŚćŻĺĺťşččżĺ?', isCreator, 'ćżé´ID:', roomId);
            
            // čˇłč˝Źĺ°čŞäš ĺŽ¤čŻŚćéĄľé˘
            uni.navigateTo({
              url: `/pages/time/TimeStudyRoomDetail?roomId=${roomId}&fromCreate=${isCreator}`
            });
          } else {
            uni.showToast({
              title: detailResponse.message || 'čˇĺčŞäš ĺŽ¤čŻŚćĺ¤ąč´?,
              icon: 'none'
            });
          }
        } else {
          uni.showToast({
            title: response.message || 'ĺ ĺĽčŞäš ĺŽ¤ĺ¤ąč´?,
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('ĺ ĺĽčŞäš ĺŽ¤ĺ¤ąč´?', error);
        uni.showToast({
          title: 'ç˝çťéčŻŻďźčŻˇéčŻ',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
        this.isLoading = false;
      }
    },
    
    // ĺˇć°čŞäš ĺŽ¤ĺčĄ?    refreshRoomList() {
      // ĺŞćĺ¨ĺčĄ¨ć¨Ąĺźä¸ćĺˇć°ĺčĄ?      if (this.studyRoomMode === 'list') {
        // éčżäşäťśéçĽĺčĄ¨çťäťśĺˇć°
        uni.$emit('refreshStudyRoomList');
      }
    },
    
    // ĺ¤çčŞäš ĺŽ¤č§ŁćŁäşäť?    handleStudyRoomDisbanded(roomId) {
      console.log('ćśĺ°čŞäš ĺŽ¤č§ŁćŁäşäť?', roomId);
      
      // ĺŚćĺ˝ĺć­Łĺ¨ćžç¤şč˘Ťč§ŁćŁçčŞäš ĺŽ¤ďźĺć˘ĺĺčĄ¨ć¨Ąĺź?      if (this.currentStudyRoom && this.currentStudyRoom.roomId === roomId) {
        // ć¸é¤çźĺ­
        uni.removeStorageSync('cachedRoom_' + roomId);
        uni.removeStorageSync('lastJoinedRoom');
        
        // éç˝Žçść?        this.currentStudyRoom = null;
        this.studyRoomMode = 'list';
        
        // ćžç¤şćç¤ş
        uni.showToast({
          title: 'čŞäš ĺŽ¤ĺˇ˛č§ŁćŁ',
          icon: 'none',
          duration: 2000
        });
      }
    },
    
    // ĺ¤çéćŠčŞäš ĺŽ?    handleSelectRoom(room) {
      // çĄŽäżĺąć§ä¸č´ć?      if (room.currentUsers !== undefined && room.currentMembers === undefined) {
        room.currentMembers = room.currentUsers;
      }
      
      this.currentStudyRoom = room;
      this.studyRoomMode = 'study';
      
      // äżĺ­ĺ˝ĺĺ ĺĽçčŞäš ĺŽ¤
      uni.setStorageSync('lastJoinedRoom', room.roomId);
      
      // čˇłč˝Źĺ°čŞäš ĺŽ¤čŻŚćéĄ?      uni.navigateTo({
        url: '/pages/time/TimeStudyRoomDetail?roomId=' + room.roomId
      });
    },
    
    // čżĺčŞäš ĺŽ¤ĺčĄ?    backToList() {
      uni.showModal({
        title: 'ćç¤ş',
        content: 'çĄŽĺŽčŚéĺşĺ˝ĺčŞäš ĺŽ¤ĺďź',
        success: (res) => {
          if (res.confirm) {
            // ć§čĄéĺşčŞäš ĺŽ¤çAPIč°ç¨
            this.exitCurrentRoom();
          }
        }
      });
    },
    
    // éĺşĺ˝ĺčŞäš ĺŽ¤
    exitCurrentRoom() {
      if (!this.currentStudyRoom) return;
      
      const roomId = this.currentStudyRoom.roomId;
      
      uni.showLoading({
        title: 'éĺşä¸­...'
      });
      
      try {
        // ä˝żç¨ĺ°čŁĺĽ˝çAPIćšćłďźçĄŽäżčŽ°ĺ˝ä¸ćł¨ćśé?        exitStudyRoom({
          roomId: roomId,
          focusTime: 1, // čłĺ°čŽ°ĺ˝1ĺéçä¸ćł¨ćśé´ä˝ä¸şĺ ĺĽčŻć?          recordFocusTime: true // ĺźşĺśčŽ°ĺ˝ä¸ćł¨ćśé´
        })
          .then(response => {
            this.handleExitSuccess();
          })
          .catch(error => {
            console.error('éĺşčŞäš ĺŽ¤ĺ¤ąč´Ľ', error);
            // ĺşéäšć§čĄéĺşćä˝?            this.handleExitSuccess();
          })
          .finally(() => {
            uni.hideLoading();
          });
      } catch (e) {
        console.error('čŻˇćąĺşé', e);
        // ĺźĺ¸¸äšć§čĄéĺşćä˝?        this.handleExitSuccess();
        uni.hideLoading();
      }
    },
    
    // ĺ¤çéĺşćĺ?    handleExitSuccess() {
      uni.showToast({
        title: 'éĺşčŞäš ĺŽ¤ćĺ',
        icon: 'success'
      });
      
      // ć¸é¤çźĺ­
      if (this.currentStudyRoom) {
        uni.removeStorageSync('cachedRoom_' + this.currentStudyRoom.roomId);
      }
      uni.removeStorageSync('lastJoinedRoom');
      
      // č§Śĺĺˇć°čŞäš ĺŽ¤ĺčĄ¨äşäť?      uni.$emit('refreshStudyRoomList');
      
      // ć¸é¤ĺ˝ĺčŞäš ĺŽ?      this.currentStudyRoom = null;
      this.studyRoomMode = 'list';
    },
    
    // ĺźĺ§ä¸ćł?    startFocus() {
      if (!this.currentStudyRoom) return;
      
      // čˇłč˝Źĺ°čŽĄćśĺ¨éĄľé˘ďźĺšśäź éčŞäš ĺŽ¤äżĄćŻ
      uni.navigateTo({
        url: '/pages/time/TimeTimer?mode=studyroom&roomId=' + this.currentStudyRoom.roomId + '&roomName=' + encodeURIComponent(this.currentStudyRoom.name)
      });
    },
    
    // čˇĺçŻĺ˘ĺç§°
    getEnvName(env) {
      switch (env) {
        case 'library':
          return 'ĺžäšŚéŚ?;
        case 'cafe':
          return 'ĺĺĄĺ?;
        case 'classroom':
          return 'ćĺŽ¤';
        case 'nature':
          return 'čŞçś';
        case 'custom':
          return 'čŞĺŽäš?;
        default:
          return 'ĺžäšŚéŚ?;
      }
    },
    
    // čˇĺçśććć?    getStatusText(status) {
      switch (status) {
        case 'focusing':
          return 'ä¸ćł¨ä¸?;
        case 'question':
          return 'ćçé?;
        case 'rest':
          return 'äźćŻä¸?;
        case 'away':
          return 'ććśçŚťĺź';
        default:
          return 'ćŞçĽ';
      }
    }
  }
};
</script>

<style>
.study-room-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* čŞäš ĺŽ¤čŻŚćĺĄç?*/
.room-detail {
  padding: 16px;
}

.room-card {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.room-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.room-title {
  flex: 1;
}

.room-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 6px;
  display: flex;
}

.room-status {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 999px;
  margin-right: 6px;
}

.status-focusing {
  background-color: #4CAF50;
}

.status-question {
  background-color: #FF9800;
}

.status-rest {
  background-color: #2196F3;
}

.status-away {
  background-color: #9E9E9E;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.room-members {
  display: flex;
  align-items: center;
  color: #666;
}

.room-environment {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.env-label {
  font-size: 14px;
  color: #666;
}

.env-value {
  font-size: 14px;
  color: #333;
}

.room-actions {
  display: flex;
  justify-content: space-between;
}

.focus-btn, .enter-btn {
  flex: 1;
  height: 40px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
}

.focus-btn {
  background-color: #4CAF50;
  color: white;
  margin-right: 12px;
}

.enter-btn {
  background-color: #5B7FFF;
  color: white;
}

.back-to-list {
  text-align: center;
  padding: 12px;
}

.back-text {
  font-size: 14px;
  color: #5B7FFF;
  text-decoration: underline;
}
</style> 
