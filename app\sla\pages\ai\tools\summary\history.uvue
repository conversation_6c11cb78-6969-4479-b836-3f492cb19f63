<template>
  <view class="history-container">
    <!-- 顶部导航栏 -->
    <view class="history-header">
      <view class="history-back" @click="goBack">
        <text class="history-back__icon">←</text>
      </view>
      <text class="history-title">历史记录</text>
      <view class="history-actions">
        <view class="history-action" @click="goToSummary">
          <text class="history-action__icon">+</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="history-content" scroll-y @scrolltolower="loadMore" refresher-enabled @refresherrefresh="refresh" :refresher-triggered="refreshing">
      <!-- 空状态 -->
      <view class="empty-state" v-if="summaryList.length === 0 && !loading">
        <view class="empty-icon">
          <text class="empty-icon__text">📄</text>
        </view>
        <text class="empty-text">暂无历史记录</text>
        <text class="empty-desc">您还没有进行过文档总结，点击下方按钮开始创建总结吧</text>
        <view class="empty-action" @click="goToSummary">
          <text class="empty-action__text">创建总结</text>
        </view>
      </view>

      <!-- 加载中 -->
      <view class="loading-state" v-if="loading && summaryList.length === 0">
        <view class="loading-animation">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 历史列表 -->
      <view class="summary-list" v-if="summaryList.length > 0">
        <view class="summary-item" v-for="(item, index) in summaryList" :key="item.summaryId">
          <view class="summary-item__content">
            <!-- 标题和时间 -->
            <view class="summary-item__header">
              <text class="summary-item__title">{{ item.title }}</text>
              <text class="summary-item__time">{{ formatTime(item.createTime) }}</text>
            </view>

            <!-- 关键词列表 -->
            <view class="summary-item__keywords-container" v-if="item.keywords && item.keywords.length > 0">
              <view class="summary-item__keywords">
                <text class="keyword-tag" v-for="(keyword, kIndex) in item.keywords" :key="kIndex">{{ keyword }}</text>
              </view>
            </view>

            <!-- 操作 -->
            <view class="summary-item__actions">
              <view class="summary-btn-container">
                <view class="summary-btn summary-btn--view" @click="viewDetail(item.summaryId)">
                  <text class="summary-btn__text">查看详情</text>
                </view>
                <view class="summary-btn summary-btn--delete" @click.stop="confirmDelete(item.summaryId)">
                  <text class="summary-btn__text">删除</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore && !loadingMore">
          <text class="load-more__text">加载更多</text>
        </view>
        <view class="loading-more" v-if="loadingMore">
          <view class="loading-more__animation">
            <view class="loading-more__dot"></view>
            <view class="loading-more__dot"></view>
            <view class="loading-more__dot"></view>
          </view>
          <text class="loading-more__text">加载中...</text>
        </view>
        <view class="load-more" v-if="!hasMore && summaryList.length > 0">
          <text class="load-more__text">已加载全部</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getSummaryList, deleteSummary } from '../../../../utils/api/aitool.js';
import { showSuccess, showError } from '../../../../utils/notificationManager.js';

export default {
  data() {
    return {
      // 历史列表
      summaryList: Array(),
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      total: 0,
      // 加载状态
      loading: false,
      loadingMore: false,
      refreshing: false,
      hasMore: true
    }
  },
  onLoad() {
    // 加载历史记录列表
    this.loadSummaryList();
  },
  methods: {
    // 返回首页
    goBack() {
      // 返回到AI工具首页
      uni.switchTab({
        url: '/pages/ai/index',
        success: () => {
          // 设置AI工具箱状态为不显示
          uni.setStorageSync('showAiToolbox', false);
        },
        fail: () => {
          // 如果失败则普通返回
          uni.navigateBack({
            delta: 1
          });
        }
      });
    },

    // 跳转到总结页面
    goToSummary() {
      uni.navigateTo({
        url: '/pages/ai/tools/summary/index'
      });
    },

    // 加载历史记录列表
    loadSummaryList() {
      if (this.loading) return;

      this.loading = true;

      getSummaryList({
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }).then(response => {
        console.log('历史记录响应:', response);

        // 获取响应数据
        const res = response;

        // 处理服务不可用
        if (res.statusCode === 503) {
          console.error('服务暂时不可用，状态码:', res.statusCode);
          showError('获取历史记录失败', '服务暂时不可用，请稍后再试');
          return;
        }

        // 处理成功响应并解析数据
        if (res && res.data && res.data.code === 200 && res.data.data) {
          // 获取分页数据
          const responseData = res.data.data;
          this.total = responseData.total || 0;

          // 处理记录列表
          const list = responseData.records || [];
          console.log('获取到的记录列表:', list);

          const processedList = list.map(item => {
            // 处理keywords字段内容
            if (typeof item.keywords === 'string') {
              try {
                item.keywords = JSON.parse(item.keywords);
              } catch (e) {
                item.keywords = [];
              }
            } else if (!Array.isArray(item.keywords)) {
              item.keywords = [];
            }
            return item;
          });

          this.summaryList = [...this.summaryList, ...processedList];
          this.hasMore = this.summaryList.length < this.total;
        } else {
          console.error('获取历史记录响应数据错误:', res);
          showError('获取历史记录失败', '数据解析错误');
        }
      }).catch(err => {
        console.error('获取历史记录失败:', err);
        uni.showToast({
          title: '获取历史记录失败: ' + (err.message || '未知错误'),
          icon: 'none',
          duration: 3000
        });
      }).finally(() => {
        this.loading = false;
        this.loadingMore = false;
        this.refreshing = false;
      });
    },

    // 加载更多
    loadMore() {
      if (this.loadingMore || !this.hasMore) return;

      this.loadingMore = true;
      this.pageNum++;
      this.loadSummaryList();
    },

    // 刷新列表
    refresh() {
      this.refreshing = true;
      this.refreshList();
    },

    // 查看详情
    viewDetail(summaryId) {
      uni.navigateTo({
        url: `/pages/ai/tools/summary/detail?id=${summaryId}`
      });
    },

    // 确认删除
    confirmDelete(summaryId) {
      uni.showModal({
        title: '确认删除',
        content: '是否确认删除此记录？',
        success: (res) => {
          if (res.confirm) {
            this.deleteSummary(summaryId);
          }
        }
      });
    },

    // 删除记录
    deleteSummary(summaryId) {
      deleteSummary(summaryId).then(response => {
        console.log('删除响应:', response);

        // 获取响应数据
        const res = response;

        // 处理成功响应并解析数据
        if (res && res.data && res.data.code === 200) {
          // 删除成功
          showSuccess('删除成功');

          // 刷新列表，从第一页重新加载数据
          this.refreshList();
        } else {
          throw new Error((res.data && res.data.message) || '删除失败');
        }
      }).catch(err => {
        console.error('删除失败:', err);
        showError('删除失败', err.message || '操作失败');
      });
    },

    // 刷新列表
    refreshList() {
      // 重置分页
      this.pageNum = 1;
      this.summaryList = [];
      this.hasMore = true;

      // 重新加载
      this.loadSummaryList();
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';

      try {
        const date = new Date(timeStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (e) {
        return timeStr;
      }
    }
  }
}
</script>

<style>
.history-container {
  min-height: 800px;
  background-color: #F8F9FA;
  display: flex;
  flex-direction: column;
}

.history-header {
  height: 56px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  background-size: 200% 200%;
  animation: gradientAnimation 10s ease infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(74, 123, 219, 0.2);
  position: relative;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.history-back {
  position: absolute;
  left: 16px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.history-back__icon {
  font-size: 20px;
  color: #FFFFFF;
  font-weight: bold;
}

.history-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
  width: 100%;
  position: absolute;
  left: 0;
}

.history-actions {
  position: absolute;
  right: 16px;
  top: 0;
  bottom: 0;
  margin: auto;
  height: 40px;
  display: flex;
  align-items: center;
  z-index: 1;
}

.history-action {
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-action__icon {
  font-size: 20px;
  color: #FFFFFF;
}

.history-content {
  flex: 1;
  padding: 16px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 16px;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background: linear-gradient(135deg, rgba(74, 123, 219, 0.05) 0%, rgba(128, 184, 245, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  border: 2px dashed rgba(74, 123, 219, 0.3);
  box-shadow: 0 8px 16px rgba(74, 123, 219, 0.05);
  animation: pulseAnimation 2s infinite alternate;
}

@keyframes pulseAnimation {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 16px rgba(74, 123, 219, 0.05);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 20px rgba(74, 123, 219, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 16px rgba(74, 123, 219, 0.05);
  }
}

.empty-icon__text {
  font-size: 40px;
  color: #4A7BDB;
}

.empty-text {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.empty-desc {
  font-size: 15px;
  color: #666666;
  margin-bottom: 32px;
  text-align: center;
  line-height: 1.5;
  max-width: 280px;
}

.empty-action {
  padding: 14px 28px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 12px rgba(74, 123, 219, 0.2);
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.empty-action:active {
  transform: translateY(2px);
  box-shadow: 0 3px 6px rgba(74, 123, 219, 0.1);
}

.empty-action__text {
  font-size: 16px;
  color: #FFFFFF;
  font-weight: bold;
}

/* 加载中状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 16px;
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.loading-dot {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background: linear-gradient(135deg, #4A7BDB 0%, #80B8F5 100%);
  margin: 0 5px;
  animation: bounceAnimation 1.4s infinite ease-in-out both;
  box-shadow: 0 2px 4px rgba(74, 123, 219, 0.2);
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounceAnimation {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  font-size: 16px;
  color: #4A7BDB;
  font-weight: bold;
}

/* 历史记录列表 */
.summary-list {
  display: flex;
  flex-direction: column;
}

.summary-item {
  background-color: #FFFFFF;
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  position: relative;
}

.summary-item:active {
  transform: translateY(2px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.summary-item__content {
  padding: 20px;
}

.summary-item__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.summary-item__title {
  font-size: 16px;
  color: #333333;
  flex: 1;
  line-height: 1.4;
  padding-right: 10px;
}

.summary-item__mindmap {
  background: linear-gradient(135deg, rgba(74, 123, 219, 0.1) 0%, rgba(128, 184, 245, 0.1) 100%);
  padding: 6px 10px;
  border-radius: 20px;
  margin-left: 12px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(74, 123, 219, 0.2);
}

.summary-item__mindmap-icon {
  font-size: 14px;
  margin-right: 4px;
}

.summary-item__mindmap-text {
  font-size: 12px;
  color: #4A7BDB;
  font-weight: bold;
}

.summary-item__keywords-container {
  margin-bottom: 16px;
}

.summary-item__keywords {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.keyword-tag {
  background-color: #F0F4FF;
  padding: 2px 6px;
  border-radius: 10px;
  margin-right: 6px;
  margin-bottom: 6px;
  border: 1px solid rgba(74, 123, 219, 0.1);
  font-size: 11px;
  color: #4A7BDB;
  display: flex;
}

.summary-item__time {
  font-size: 12px;
  color: #999999;
  white-space: nowrap;
}

.summary-item__actions {
  display: flex;
  margin-top: 12px;
}

.summary-btn-container {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.summary-btn {
  padding: 6px 0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
  flex: 1;
}

.summary-btn--view {
  background-color: #4A7BDB;
  margin-right: 10px;
}

.summary-btn--view:active {
  background-color: #3A6BCB;
}

.summary-btn--delete {
  background-color: #F44336;
}

.summary-btn--delete:active {
  background-color: #E53935;
}

.summary-btn__text {
  font-size: 13px;
  color: #FFFFFF;
  font-weight: bold;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 16px 0;
}

.load-more__text {
  font-size: 14px;
  color: #999999;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 0;
}

.loading-more__animation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.loading-more__dot {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  background-color: #4A7BDB;
  margin: 0 3px;
  animation: bounceSmallAnimation 1.4s infinite ease-in-out both;
}

.loading-more__dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-more__dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounceSmallAnimation {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-more__text {
  font-size: 14px;
  color: #4A7BDB;
}
</style>
</template>

